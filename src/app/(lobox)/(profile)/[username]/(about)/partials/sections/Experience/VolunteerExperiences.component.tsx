import React, { useMemo } from 'react';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import {
  addDisplayDuration,
  experienceNormalizer,
} from '@shared/utils/experience.utils';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import { VOL_EXPERIENCE } from 'shared/constants/profileModalsKeys';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import useProfilePage from 'shared/hooks/useProfilePage';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useProfileSection } from '../../hooks/useProfileSection';
import type { Experience } from '@shared/types/experience';

const VolunteerExperiences = (): JSX.Element => {
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();
  const { checkSectionVisibility } = useProfilePage();
  const visibleCurrentExperience = checkSectionVisibility(
    'visibleCurrentExperience'
  );
  const visiblePastExperience = checkSectionVisibility('visiblePastExperience');
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(VOL_EXPERIENCE, profileSectionsStepKeys.VOL_EXPERIENCE);
  const { isAuthUser } = useProfilePage();

  const experiences = useMemo(
    () =>
      data?.volunteers?.map((item: Experience) => ({
        ...item,
        workPlaceTypeLabel: item.workPlaceType
          ? t(item.workPlaceType)
          : undefined,
        companyPageId:
          item?.companyPageId ||
          `${item.companyName?.trim().toLowerCase()}_temp`,
      })),
    [data?.volunteers, t]
  );

  const volunteerExperiences = useMemo(
    () =>
      experiences
        ?.filter((i: Experience) => {
          if (visiblePastExperience && !i.currentlyWorking) {
            return true;
          }

          return !!(visibleCurrentExperience && i.currentlyWorking);
        })
        .reduce(experienceNormalizer, [])
        .map(addDisplayDuration),
    [experiences, visibleCurrentExperience, visiblePastExperience]
  );

  return (
    <AboutSectionLayout
      data={volunteerExperiences}
      isLoading={isLoading}
      title={t('volunteer')}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
        />
      )}
    </AboutSectionLayout>
  );
};

export default VolunteerExperiences;
