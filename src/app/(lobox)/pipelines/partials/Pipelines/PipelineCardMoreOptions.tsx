import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import IconButton from '@shared/uikit/Button/IconButton';
import CheckBox from '@shared/uikit/CheckBox';
import Divider from '@shared/uikit/Divider';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import Tooltip from '@shared/uikit/Tooltip';
import Typography from '@shared/uikit/Typography';
import { getVendorsIncluded } from '@shared/utils/api/company';
import { QueryKeys } from '@shared/utils/constants';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { JobAPIProps } from '@shared/types/jobsProps';
import type { FC } from 'react';
import useReactQuery from '@shared/utils/hooks/useReactQuery';

interface PipelineCardMoreOptionsProps {
  isBulk: boolean;
  isSelected: boolean;
  pipeline: JobAPIProps;
}

const PipelineCardMoreOptions: FC<PipelineCardMoreOptionsProps> = ({
  isBulk,
  isSelected,
  pipeline,
}) => {
  const { t } = useTranslation();

  const { data: vendorsIncluded } = useReactQuery<any>({
    action: {
      apiFunc: () => getVendorsIncluded(),
      key: [QueryKeys.getVendorsIncluded],
    },
  });

  const exclude = vendorsIncluded?.length ? [] : ['submit_to_vendor'];

  const { actions, onAction } = useRecruiterJobMoreActions({
    exclude: [
      pipeline?.status !== 'OPEN' &&
        !vendorsIncluded?.length &&
        'submit_to_vendor',
      pipeline?.status !== 'OPEN' && 'link_candidate',
    ]?.filter(Boolean),
  });

  const isUnpublished = pipeline.status === 'UNPUBLISHED';

  if (isBulk) {
    return (
      <Tooltip
        placement="top"
        hidden={!isUnpublished}
        trigger={
          <CheckBox
            disabled={isUnpublished}
            value={!isUnpublished && isSelected}
          />
        }
      >
        <Typography
          size={13}
          font="400"
          color="skeletonBg"
          className="max-w-[104px] text-center"
        >
          {t('unpublished_job_cant_be_selected')}
        </Typography>
      </Tooltip>
    );
  }

  return (
    <PopperMenu
      placement="bottom-end"
      closeOnScroll
      buttonComponent={<IconButton type="fas" name="ellipsis-h" size="md" />}
    >
      {actions.map((item) => (
        <>
          <PopperItem
            key={`job_option_${item.label}`}
            onClick={() => onAction(item.label, pipeline as any)}
            iconName={item.icon}
            iconType="far"
            label={t(item.label)}
            iconSize={20}
          />
          {item.hasDivider && <Divider />}
        </>
      ))}
    </PopperMenu>
  );
};

export default PipelineCardMoreOptions;
