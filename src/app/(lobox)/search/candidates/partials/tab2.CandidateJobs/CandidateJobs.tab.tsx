import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import { useMemo, useState, type FC } from 'react';
import BaseBusinessJobCard from '@shared/components/molecules/BusinessJobCard/BaseBusinessJobCard';
import BusinessJobCardActions from '@shared/components/molecules/BusinessJobCard/BusinessJobCardActions';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import PilledTabBar from '@shared/components/molecules/PilledTabBar/PilledTabBar';
import { useAsyncPickerModal } from '@shared/components/Organism/AsyncPickerModal';
import { useLinkJobsToCandidate } from '@shared/components/Organism/AsyncPickerModal/presets/job/hooks';
import { getAsyncSingleJobPickerModalProps } from '@shared/components/Organism/AsyncPickerModal/presets/job/utils';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import ComingSoon from '@shared/svg/ComingSoon';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import Button from '@shared/uikit/Button';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import Flex from '@shared/uikit/Flex/index';
import {
  getCandidacyLinkedJobs,
  getUserApplications,
  updateJobTags,
} from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from './CandidateJobs.module.scss';
import CandidateJobsSkeleton from './CandidateJobs.skeleton';
import type { PilledTabItem } from '@shared/components/molecules/PilledTabBar/PilledTabBar';
import type { BaseCandidateSectionProp } from '@shared/types/candidates';
import type { SingleJobAPIProps } from '@shared/types/jobsProps';

type TabKeys = 'applied' | 'linked' | 'suggested';

export const CandidateJobsTab: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const [tab, setTab] = useState<TabKeys | ''>('');
  const list: PilledTabItem<TabKeys>[] = [
    { id: 'applied', label: t('applied') },
    { id: 'linked', label: t('linked') },
    // { id: 'suggested', label: t('suggested') },
  ];

  const linkJobsAsyncPicker = useLinkJobsToCandidate(candidate);

  const jobPicker = useAsyncPickerModal({
    ...getAsyncSingleJobPickerModalProps(t),
    local: true,
    onSuccess(data) {
      appDispatch({
        type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
        payload: {
          isOpen: true,
          tabs: [
            ShareEntityTab.COPY_LINK,
            ShareEntityTab.SHARE_VIA_MESSAGE,
            ShareEntityTab.SHARE_VIA_EMAIL,
          ],
          entityData: {
            attachment: {
              type: ShareEntities.JOB,
              data: {
                ...data.select,
                categoryName: data.select.category,
                title: { label: data.select.title },
                location: { label: data.select.location },
                workPlaceType: { label: 'workPlaceType' },
                isBusiness: true,
              },
            },
          },
        },
      });
    },
  });

  const appliedJobsQuery = useReactQuery<Array<SingleJobAPIProps>>({
    action: {
      apiFunc: async () => {
        const appliedJobs = await getUserApplications(
          candidate.profile.originalId
        );

        return appliedJobs.map((a) => a.job);
      },
      params: {
        status: 'OPEN',
        containsLastActivity: true,
      },
      key: [
        QueryKeys.getCandidateAppliedJobsList,
        candidate.profile.originalId,
      ],
    },
    config: {
      enabled: !!candidate.profile.originalId,
      refetchOnWindowFocus: false,
    },
  });

  const linkedJobsQuery = useReactQuery({
    action: {
      apiFunc: () => getCandidacyLinkedJobs(candidate.id),
      key: [QueryKeys.getCandidateRecruiterJobsList, candidate.id],
      params: {
        status: 'OPEN',
      },
    },
    config: {
      enabled: !!candidate.id,
      refetchOnWindowFocus: false,
    },
  });

  const activeTab = useMemo(() => {
    if (tab) return tab;
    if (!appliedJobsQuery.isLoading && appliedJobsQuery.data?.length)
      return 'applied';
    if (!linkedJobsQuery.isLoading && linkedJobsQuery.data?.length)
      return 'linked';

    return 'applied';
  }, [
    tab,
    appliedJobsQuery.isLoading,
    appliedJobsQuery.data?.length,
    linkedJobsQuery.isLoading,
    linkedJobsQuery.data?.length,
  ]);

  const emptyLinkedJobs = useMemo(
    () => (
      <EmptySearchResult
        title={t('no_jobs')}
        sectionMessage={t('candidate_not_linked_to_any_job')}
        className={classes.emptySearch}
      >
        <Button
          onClick={linkJobsAsyncPicker.open}
          className={classes.emptySearchBtn}
          leftIcon="link"
          label={t('link_jobs')}
        />
      </EmptySearchResult>
    ),
    [t, linkJobsAsyncPicker]
  );

  const emptyAppliedJobs = useMemo(
    () => (
      <EmptySearchResult
        title={t('no_jobs')}
        sectionMessage={t('candidate_not_applied_to_any_job')}
        className={classes.emptySearch}
      >
        <Button
          onClick={jobPicker.open}
          className={classes.emptySearchBtn}
          leftIcon="link"
          label={t('share_job')}
        />
      </EmptySearchResult>
    ),
    [t, jobPicker.open]
  );

  if (linkedJobsQuery.isLoading || appliedJobsQuery.isLoading) {
    return <CandidateJobsSkeleton />;
  }
  if (linkedJobsQuery.data?.length || appliedJobsQuery.data?.length) {
    return (
      <Flex className={classes.section}>
        <PilledTabBar
          list={list}
          state={activeTab}
          onChange={setTab}
          className={classes.tabBar}
        />
        {activeTab === 'applied' ? (
          appliedJobsQuery.data?.length ? (
            <List
              data={appliedJobsQuery.data}
              refetchJobData={linkedJobsQuery.refetch}
            />
          ) : (
            emptyAppliedJobs
          )
        ) : null}
        {activeTab === 'linked' ? (
          linkedJobsQuery.data?.length ? (
            <List
              data={linkedJobsQuery.data}
              refetchJobData={linkedJobsQuery.refetch}
            />
          ) : (
            emptyLinkedJobs
          )
        ) : null}
        {activeTab === 'suggested' ? (
          <EmptySectionInModules
            title={t('coming_3dot')}
            image={<ComingSoon />}
          />
        ) : null}
      </Flex>
    );
  }

  return <Flex className={classes.section}>{emptyLinkedJobs}</Flex>;
};

const List: FC<{
  data: Array<SingleJobAPIProps>;
  refetchJobData: VoidFunction;
}> = ({ data, refetchJobData }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const handleViewJob = (id: string, tab?: string) =>
    router.push(`/search/my-jobs?currentEntityId=${id}${tab ? `#${tab}` : ''}`);

  return data.map((job) => {
    const { projects = [], creatorUser } = job;
    const projectsNames = projects.map((p) => p.title).join(', ');

    return (
      <BaseBusinessJobCard
        key={job.id}
        id={job.id}
        title={job.title}
        image={job.pageCroppedImageUrl}
        username={job.pageTitle}
        category={job.categoryName}
        location={job.location?.title}
        creator={{
          name: `${creatorUser?.name} ${creatorUser?.surname}`,
          username: creatorUser?.username ?? '',
        }}
        projects={projectsNames}
        collaboratorsCount={job.collaborators.length}
        applicantsCount={job.applicantsCount}
        candidatesCount={job.candidatesCount}
        createdAt={dayjs(job.createdDate).toString()}
        lastUpdate={dayjs(job.changeStatusDateTime).toString()}
        lastActivity={job?.lastActivity}
        badgeActions={{
          onApplicantsClick: () => handleViewJob(job.id, 'applicants'),
          onAssigneesClick: () => handleViewJob(job.id, 'assignees'),
          onCandidatesClick: () => handleViewJob(job.id, 'candidates'),
        }}
        status={job.status}
        priority={job.priority}
        tags={
          <HorizontalTagList
            tags={job.tags}
            title={t('job_tags')}
            editable
            onSuccess={refetchJobData}
            apiFunc={(body) => updateJobTags(job.id, body.tags)}
          />
        }
        actions={<BusinessJobCardActions job={job} />}
      />
    );
  });
};
