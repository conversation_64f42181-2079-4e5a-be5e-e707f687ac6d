import { useRouter } from 'next/navigation';
import React, { useCallback, type FC } from 'react';
import CandidateCard, {
  CandidateCardActions,
} from '@shared/components/molecules/CandidateCard';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import SendMessageButton from '@shared/components/molecules/SendMessageButton';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import PlanRestrictionCard from '@shared/components/Organism/PlanRestrictionCard';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { FeatureName } from '@shared/types/planRestriction';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex/index';
import {
  getSimilarCandidateItem,
  getSimilarCandidates,
} from '@shared/utils/api/candidates';
import { QueryKeys, routeNames } from '@shared/utils/constants';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import SimilarCandidatesSkeleton from './CandidateSimilar.skeleton';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import type { BaseCandidateSectionProp } from '@shared/types/candidates';

export const CandidateSimilarTab: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const getQueryValue = useGetNormalizedArrayQuery();
  const similarEntityId = getQueryValue('similarEntityId');
  const appDispatch = useGlobalDispatch();
  const router = useRouter();

  const { data: similarUsers, isLoading } = useReactInfiniteQuery<any>(
    [QueryKeys.getSimilarCandidates, candidate?.id],
    {
      func: getSimilarCandidates,
      size: 10,
      extraProps: {
        id: similarEntityId || candidate?.id,
      },
    }
  );

  const handleOpenManagerModal = useCallback(
    (tab: CandidateManagerTabkeys, currentIndex: number) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab,
          currentIndex,
          totalElements: similarUsers?.length,
          apiFunc: getSimilarCandidateItem,
          entityId: candidate?.id,
        },
      });
    },
    [appDispatch, similarUsers?.length, candidate?.id]
  );

  return (
    <>
      <PlanRestrictionCard
        className="mt-32"
        featuresName={[FeatureName.SEE_SIMILAR_CANDIDATE]}
      />
      {isLoading ? (
        <SimilarCandidatesSkeleton />
      ) : similarUsers.length > 0 ? (
        <Flex className="mt-32 gap-20">
          {similarUsers.map((user, index) => (
            <CandidateCard
              key={user.id}
              enableLinks
              avatar={user?.profile?.croppedImageUrl}
              firstText={user?.profile?.fullName}
              secondText={user?.profile?.usernameAtSign}
              thirdText={user.profile?.occupation?.label}
              fourthText={cleanRepeatedWords(
                user?.profile?.location?.title || ''
              )}
              FirstTextWrapper={
                !user.profile.username ? IsManualWrapper : undefined
              }
              onBadgeClick={(tab: CandidateManagerTabkeys) =>
                handleOpenManagerModal(tab, index)
              }
              treeDotMenu={<CandidateCardActions candidate={user} />}
              showBadges
              showTags
              showActions
              onClick={() =>
                router.push(
                  `${routeNames.searchCandidates}?currentEntityId=${user.id}`
                )
              }
            >
              <Flex className="!flex-row gap-12">
                <SendMessageButton
                  className="flex-1"
                  disabled={!user?.username}
                  object={{
                    id: user.originalId,
                    croppedImageUrl: user.croppedImageUrl,
                    fullName: user.fullName,
                    username: user.username,
                    isPage: false,
                  }}
                  fullWidth
                />
                <Button
                  className="flex-1"
                  label={t('manage')}
                  leftIcon="user-cog"
                  fullWidth
                  onClick={() => handleOpenManagerModal('notes', index)}
                />
              </Flex>
            </CandidateCard>
          ))}
        </Flex>
      ) : (
        <Flex className="mt-32 gap-20 !flex-1">
          <EmptySearchResult
            title={t('no_similar_found')}
            sectionMessage={t('no_similar_candidate_found')}
            className="!py-32 px-0 !m-0"
          />
        </Flex>
      )}
    </>
  );
};
