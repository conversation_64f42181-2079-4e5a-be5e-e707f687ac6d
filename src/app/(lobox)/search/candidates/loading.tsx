import SearchListWithDetailsSkeleton from '@shared/components/layouts/SearchListWithDetailsLayout/SearchListWithDetails.skeleton';
import CandidateCardSkeleton from '@shared/components/molecules/CandidateCard/CandidateCardSkeleton';
import Flex from '@shared/uikit/Flex/index';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import CandidateDetailsSkeleton from './partials/CandidateDetails.skeleton';
import classes from './styles.module.scss';

const skeletons = Array.from({ length: 5 }, (_, i) => i);

export default function Loading() {
  return (
    <>
      <Flex className={classes.filtersSkeleton}>
        <Skeleton />
        <Skeleton />
        <Skeleton />
        <Skeleton />
        <Skeleton />
        <Skeleton />
      </Flex>
      <SearchListWithDetailsSkeleton
        classNames={{
          contentRoot: classes.contentRoot,
          detailsRoot: classes.scrollbarFix,
          headerRoot: classes.headerRoot,
          list: cnj(classes.scrollbarFix, classes.list),
        }}
        listComponent={
          <Flex className={classes.listSkeleton}>
            <Flex className={classes.listHeaderSkeleton}>
              <Skeleton className="rounded !w-[120px] !h-[37px] !my-auto" />
            </Flex>
            {skeletons?.map((item: number) => (
              <CandidateCardSkeleton key={item} showBadges />
            ))}
          </Flex>
        }
        detailsComponent={<CandidateDetailsSkeleton />}
      />
    </>
  );
}
