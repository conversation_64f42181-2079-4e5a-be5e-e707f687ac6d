import React from 'react';
import JobFullDetailsSkeleton from '@shared/components/molecules/Job/JobFullDetailsSkeleton';
import { BusinessJobCardSkeleton } from 'shared/components/molecules/BusinessJobCard';
import Flex from 'shared/uikit/Flex';
import classes from './RecruiterJobDetails.module.scss';

const RecruiterJobDetailsSkeleton: React.FC = () => {
  return (
    <Flex className={classes.detailsRoot}>
      <BusinessJobCardSkeleton showTags isMain />
      <JobFullDetailsSkeleton />
    </Flex>
  );
};

export default RecruiterJobDetailsSkeleton;
