import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import IconButton from '@shared/uikit/Button/IconButton';
import Divider from '@shared/uikit/Divider';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { SingleJobAPIProps } from '@shared/types/jobsProps';
import type { FC } from 'react';

interface RecruiterJobDetailsCardMoreOptionsProps {
  job: SingleJobAPIProps;
}

const RecruiterJobDetailsCardMoreOptions: FC<
  RecruiterJobDetailsCardMoreOptionsProps
> = ({ job }) => {
  const { t } = useTranslation();

  const { actions, onAction } = useRecruiterJobMoreActions({
    exclude: [
      job?.status !== 'OPEN' && 'submit_to_vendor',
      job?.status !== 'OPEN' && 'link_candidate',
    ].filter(Boolean),
  });

  return (
    <PopperMenu
      placement="bottom-end"
      closeOnScroll
      buttonComponent={<IconButton type="fas" name="ellipsis-h" size="md" />}
    >
      {actions.map((item) => (
        <>
          <PopperItem
            key={`job_option_${item.label}`}
            onClick={() => onAction(item.label, job)}
            iconName={item.icon}
            iconType="far"
            label={t(item.label)}
            iconSize={20}
          />
          {item.hasDivider && <Divider />}
        </>
      ))}
    </PopperMenu>
  );
};

export default RecruiterJobDetailsCardMoreOptions;
