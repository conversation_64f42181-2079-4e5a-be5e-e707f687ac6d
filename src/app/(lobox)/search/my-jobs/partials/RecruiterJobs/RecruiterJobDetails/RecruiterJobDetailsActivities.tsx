import { useQuery } from '@tanstack/react-query';
import ActivityItem from 'shared/components/molecules/ActivityItem';
import Flex from 'shared/uikit/Flex';
import { Endpoints, QueryKeys } from 'shared/utils/constants';
import request from 'shared/utils/toolkit/request';
import classes from './RecruiterJobDetailsStyles.module.scss';
import type { FC } from 'react';
import type { ActivityProps } from 'shared/types/activityProps';
import type { PaginateResponse } from 'shared/types/response';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import Section from '@shared/components/molecules/Section';

interface RecruiterJobDetailsActivitiesProps {
  jobId: string;
}

const RecruiterJobDetailsActivities: FC<RecruiterJobDetailsActivitiesProps> = ({
  jobId,
}) => {
  const { data, isLoading } = useQuery({
    queryKey: [QueryKeys.jobActivities, jobId],
    queryFn: (params) =>
      request.get<PaginateResponse<ActivityProps>>(
        Endpoints.App.Job.getActivities(params.queryKey[1])
      ),
  });

  return (
    <Section hasSearchInput={false} className="!flex-1">
      <Flex className={classes.applicantsRoot}>
        <CardWrapper>
          {data?.data.content.map((activity) => (
            <ActivityItem key={`activity_${activity.id}`} item={activity} />
          ))}
        </CardWrapper>
      </Flex>
    </Section>
  );
};

export default RecruiterJobDetailsActivities;
