'use client';

import React from 'react';
import SearchListSkeleton from 'shared/components/Organism/SearchList/SearchListSkeleton';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from 'shared/uikit/Flex';
import Skeleton from 'shared/uikit/Skeleton';
import cnj from 'shared/uikit/utils/cnj';
import classes from './loading.module.scss';
import RecruiterJobDetailsSkeleton from './partials/RecruiterJobs/RecruiterJobDetailsSkeleton';

const list = Array(6).fill(0);

export default function loading() {
  return (
    <>
      <Flex className={classes.linksRootShrink}>
        <Flex className={classes.searchFilterContent}>
          <IconButton
            name="chevron-left"
            type="far"
            className={classes.backBtn}
          />
          {list.map((_, i) => (
            <Skeleton key={`jobs_empty_${i}`} className={classes.skeleton} />
          ))}
          <Skeleton className={classes.modalButtonSkeleton} />
        </Flex>
      </Flex>
      <Flex className={cnj(classes.contentRoot, classes.maxWidth)}>
        <Flex className={classes.jobsListWithDetails}>
          <Flex className={classes.list}>
            <Flex className={classes.wrapper}>
              <SearchListSkeleton
                entity="recruiterJobs"
                title="Jobs"
                itemCount={6}
                noTopBottomPadding
              />
            </Flex>
          </Flex>
          <Flex className={classes.details}>
            <RecruiterJobDetailsSkeleton />
          </Flex>
        </Flex>
      </Flex>
    </>
  );
}
