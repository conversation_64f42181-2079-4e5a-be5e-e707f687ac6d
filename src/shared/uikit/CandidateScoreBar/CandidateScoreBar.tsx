import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '../Flex';
import Typography from '../Typography';
import styles from './CandidateScoreBar.module.scss';

type CandidateScoreBarProps = {
  score: number; // must be between 0 and 100
  showTitle?: boolean;
};

const getBubblePosition = (score: number) => {
  if (score <= 0) return '0px';
  if (score >= 100) return 'calc(100% - 44px)';
  const margin = 8;
  const clampedPosition = Math.max(margin, Math.min(100 - margin, score));
  return `calc(${clampedPosition}% - 22px)`;
};

const CandidateScoreBar: React.FC<CandidateScoreBarProps> = ({
  score,
  showTitle = true,
}) => {
  const { t } = useTranslation();
  const clampedScore = Math.max(0, Math.min(100, score));

  return (
    <Flex className={styles.container}>
      <Typography
        className="mb-8"
        color="colorIconForth2"
        size={14}
        height={14}
      >
        {showTitle && t('candidate_score_vs_job')}
      </Typography>
      <Flex className={styles.barWrapper}>
        <Flex className={styles.bar} />
        <Flex
          className={styles.scoreBubble}
          style={{
            left: getBubblePosition(clampedScore),
          }}
        >
          <Typography height={15} font="bold" color="smoke_coal">
            {clampedScore}%
          </Typography>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default CandidateScoreBar;
