import { isValidDate } from '@shared/utils/toolkit/isValidDate';
import { displayDuration } from 'shared/utils/toolkit/date-helpers';
import datesDuration from 'shared/utils/toolkit/datesDuration';
import formatDate from 'shared/utils/toolkit/formatDate';

export const convertCourseApiToForm = (item) => {
  const {
    institutionPageId,
    institutionName,
    title,
    titleLookupId,
    startDate,
    endDate,
    id,
    description,
    pageCroppedImageUrl,
  } = item;

  return {
    institution: {
      label: institutionName,
      value: institutionPageId,
      image: pageCroppedImageUrl,
    },
    title: { label: title, value: titleLookupId },
    id,
    endDate,
    startDate,
    description,
  };
};

export const courseNormalizer = (item) => {
  const {
    institutionPageId,
    institutionName,
    title,
    startDate,
    endDate,
    id,
    description,
    pageCroppedImageUrl,
  } = item;
  const realData = convertCourseApiToForm(item);

  const formattedEndDate = endDate ? formatDate(endDate) : 'Present';
  const formattedStartDate = formatDate(startDate);
  const durationObj = datesDuration({
    startDate,
    endDate: endDate || new Date().toISOString(),
  });
  const duration = durationObj ? displayDuration(durationObj) : '';

  return {
    id,
    image: pageCroppedImageUrl,
    firstText: title,
    fourthTextAdditionalProps: { objectId: institutionPageId },
    secondText: isValidDate(startDate)
      ? `${formattedStartDate} - ${formattedEndDate}`
      : 'No date entered',
    secondTextHelper: isValidDate(startDate) ? duration : undefined,
    fourthText: institutionName,
    longText: description,
    realData,
    objectId: institutionPageId,
  };
};
