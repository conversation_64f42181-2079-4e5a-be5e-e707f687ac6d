import type {
  PipelineAutoNote,
  PipelineAutoNoteResponse,
} from '@shared/utils/api/pipeline';

export function NormalizerNoteData(
  note?: PipelineAutoNoteResponse
): PipelineAutoNote | null {
  if (!note) return null;

  return {
    id: note?.pipeline?.id,
    pipelineId: note?.pipeline?.id,
    visibility: note?.visibility,
    text: note?.text,
    fileIds: note?.fileIds ?? [],
    createdAt: note?.user?.birthDate,
    updatedAt: note?.user?.birthDate,
    pipeline: note?.pipeline,
    user: {
      id: note?.user?.id,
      name: note?.user?.name,
      role: note?.user?.occupationName,
      avatarUrl: note?.user?.croppedImageUrl,
    },
  };
}
