import Endpoints from 'shared/utils/constants/endpoints';
import { projectsEndpoints } from '../constants/servicesEndpoints';
import beforeCacheUserInfo from '../normalizers/beforeCacheUserInfo';
import searchFiltersNormalizer from '../normalizers/searchFiltersNormalizer';
import request from '../toolkit/request';
import type { SubmittedUnSubmitted } from '@shared/types/job';
import type { ProjectProps, ProjectStatusType } from '@shared/types/project';
import type { PaginateResponse } from '@shared/types/response';
import type {
  MutateCreateProjectFormProps,
  ProjectEntitiyType,
} from 'shared/types/projectsProps';

export const createProject = async (
  projectData: MutateCreateProjectFormProps
) => {
  const { data } = await request.post(Endpoints.App.Project.base, projectData);

  return data;
};

export const getProjectsList = async (
  params: any
): Promise<PaginateResponse<ProjectProps>> => {
  const { data } = await request.get<PaginateResponse<ProjectProps>>(
    Endpoints.App.Project.base,
    {
      params,
    }
  );

  return data;
};

export const addJobsToProject = async ({
  jobIds,
  projectId,
}: {
  projectId: string;
  jobIds: string[];
}): Promise<any> => {
  const { data } = await request.put(
    `${Endpoints.App.Project.addJobs}/${projectId}`,
    {
      jobIds,
    }
  );

  return data;
};

export const getProjectEntitiesAPI = async <T>({
  variant,
  projectId,
  ...params
}: {
  variant: ProjectEntitiyType;
  projectId: string;
  page: number;
  onlyDone: boolean;
}): Promise<PaginateResponse<T>> => {
  const { data } = await request.get<PaginateResponse<T>>(
    projectAPIs[variant](projectId),
    { params }
  );

  return data;
};

export const getProject = async (params: {
  id: string;
  containsLastActivity?: boolean;
}): Promise<ProjectProps> => {
  const { id, ...restParams } = params;

  const { data } = await request.get<ProjectProps>(
    projectsEndpoints.getProject(id),
    { params: restParams }
  );

  return {
    ...data,
    collaboratorUsers: data.collaboratorUsers.map((collab) =>
      beforeCacheUserInfo(collab)
    ),
  };
};
export const updateAssignees = async (
  projectId: string,
  userIds: string[]
): Promise<ProjectProps> => {
  const { data } = await request.put<ProjectProps>(
    Endpoints.App.Project.collaborators(projectId),
    { userIds }
  );

  return data;
};
export const projectChangeStatus = async (jobData: {
  id: string;
  status: ProjectStatusType;
}): Promise<any> => {
  const { status, id } = jobData;
  const api =
    status === 'OPEN'
      ? Endpoints.App.Project.openStatusProject(id)
      : Endpoints.App.Project.archiveStatusProject(id);
  const { data } = await request.put(api);

  return data;
};
export const projectCheckValidity = async (title: string): Promise<any> => {
  const { data } = await request.get(
    `${Endpoints.App.Project.checkTitle}?title=${title}`
  );

  return data?.value;
};

export const editProject = async (
  id: string,
  projectData: MutateCreateProjectFormProps
) => {
  const { data } = await request.put(
    Endpoints.App.Project.edit(id),
    projectData
  );

  return data;
};

export const deleteProject = async (id: string) => {
  const { data } = await request.delete(projectsEndpoints.getProject(id));

  return data;
};
export const updateProjectTags = async (id: string, tags: string[]) => {
  const { data } = await request.put(Endpoints.App.Project.edit(id), { tags });

  return data;
};
export const searchProjectActivities = async <T>(params: {
  projectId: string;
  page: number;
  text?: string;
}): Promise<PaginateResponse<T>> => {
  const { data } = await request.get<PaginateResponse<T>>(
    Endpoints.App.Job.searchActivities,
    {
      params,
    }
  );

  return data;
};

export const getSubmittedUnsubmitted = async ({
  params,
}: {
  params?: {
    vendorId: number;
    text?: string;
  };
} = {}): Promise<SubmittedUnSubmitted[]> => {
  const { data } = await request.get(Endpoints.App.Job.submittedUnsubmitted, {
    params,
  });

  return data;
};

export const batchUpdateProjects = async (params: {
  jobIds: string[];
  projectIds: string[];
}): Promise<any> => {
  const { data } = await request.put(projectsEndpoints.batchUpdateProjects, {
    ...params,
  });

  return data;
};

export const searchProjects = async (
  params: any
): Promise<PaginateResponse<ProjectProps>> => {
  const { data } = await request.get<PaginateResponse<ProjectProps>>(
    projectsEndpoints.search,
    { params }
  );

  return data;
};

export const searchProjectFilters = async (
  params: any
): Promise<PaginateResponse<ProjectProps>> => {
  const { data } = await request.get<PaginateResponse<ProjectProps>>(
    projectsEndpoints.filter,
    params
  );

  return searchFiltersNormalizer.projectsFilters(data);
};

const projectAPIs: { [key in ProjectEntitiyType]: any } = {
  applicants: Endpoints.App.Job.getProjectApplicants,
  candidates: Endpoints.App.Job.getProjectCandidates,
  activities: Endpoints.App.Job.getProjectActivities,
  jobs: Endpoints.App.Job.getJobProjects,
  pastMettings: Endpoints.App.Project.getPastMeetings,
  upcomingMettings: Endpoints.App.Project.getUpcomingMeetings,
  todos: Endpoints.App.Project.getTodos,
};
