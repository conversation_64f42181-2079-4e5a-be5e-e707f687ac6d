import React, { useState } from 'react';
import { type RoleType } from '@shared/types/page';
import * as CompanyApi from '@shared/utils/api/company';
import { APP_ENTITIES } from '@shared/utils/constants/app-entities';
import { ToggleNotificationList } from '@shared/utils/constants/NotificationVariants';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Layout, {
  Avatar,
  type INotificationProps,
} from '../Notification.layout';
import { renderNotificationText } from '../utils/renderNotificationText';

type TaskNotificationProps = INotificationProps<{
  userTitle: string;
  userType: 'PAGE';
  pageId: string;
  pageTitle: string;
  portalRoles: RoleType[];
  portalRole: RoleType;
  portalNames: [];
  vendorClientId: string;
  companyRole?: 'CLIENT' | 'VENDOR';
}>;

const VendorRelationshipRequested = ({
  data,
  onSeen,
  onDelete,
  menuActions,
  updateNotification,
  decreaseBadgeCount,
}: TaskNotificationProps) => {
  const { t } = useTranslation();
  const [result, setResult] = useState<any>(
    data.isAcceptedAlready
      ? { text: `${t('you_accepted_role')}.` }
      : data.isDeclinedAlready
        ? { text: `${t('you_declined_role')}.`, variant: 'error' }
        : undefined
  );

  const { mutate: acceptRequest } = useReactMutation({
    apiFunc: CompanyApi.acceptRequest,
  });
  const { mutate: declineRequest } = useReactMutation({
    apiFunc: CompanyApi.declineRequest,
  });
  const handleAcceptMemberShip = () => {
    onSeen?.();
    acceptRequest(
      { id: data.vendorClientId },
      {
        onSuccess: () => {
          setResult({ text: `${t('you_accepted_role')}.` });
          updateNotification?.({ isAcceptedAlready: true, seen: true });
          decreaseBadgeCount?.();
          onDelete();
        },
      }
    );
  };

  const handleDeclineMemberShip = () => {
    onSeen?.();
    declineRequest(
      { id: data.vendorClientId },
      {
        onSuccess: () => {
          setResult({ text: `${t('you_declined_role')}.`, variant: 'error' });
          updateNotification?.({ isDeclinedAlready: true, seen: true });
          decreaseBadgeCount?.();
          onDelete();
        },
      }
    );
  };

  const hasToggleNotification = ToggleNotificationList.includes(data.type);

  return (
    <Layout
      hasToggleNotification={hasToggleNotification}
      menuActions={menuActions}
      onClick={onSeen}
      icon={
        <Avatar
          isCompany={data?.userType === APP_ENTITIES.page}
          src={data?.croppedImageUrl}
        />
      }
      description={renderNotificationText(
        translateReplacer(
          data.companyRole === 'VENDOR'
            ? t('person_from_page_requested_vendor_relationship')
            : t('person_from_page_requested_vendor_relationship'),
          [data?.userTitle, t(`${data?.portalRole}_label`), data?.pageTitle]
        ),
        [data?.userTitle, t(`${data?.portalRole}_label`), data?.pageTitle]
      )}
      date={data?.createdDate}
      seen={data?.seen}
      primaryAction={
        !result
          ? {
              label: t('accept'),
              onClick: handleAcceptMemberShip,
            }
          : undefined
      }
      secondaryAction={
        !result
          ? {
              label: t('decline'),
              onClick: handleDeclineMemberShip,
            }
          : undefined
      }
    />
  );
};

export default VendorRelationshipRequested;
