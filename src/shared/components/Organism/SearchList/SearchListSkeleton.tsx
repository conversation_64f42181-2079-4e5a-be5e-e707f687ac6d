import React from 'react';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import classes from './index.module.scss';
import SearchListHeader from './partials/SearchListHeader';
import SearchListItemSkeleton from './SearchListItem.skeleton';
import type { ISearchEntity } from '@shared/hooks/searchFilters/useSearchResultWithFilters';

interface SearchListSkeletonProps {
  entity?: ISearchEntity;
  title?: string;
  itemCount?: number;
  className?: {
    root?: string;
    header?: string;
    pagination?: string;
  };
  noTopBottomPadding?: boolean;
  ItemSkeleton?: (props: any) => React.ReactElement;
}

const SearchListSkeleton: React.FC<SearchListSkeletonProps> = ({
  entity = 'recruiterJobs',
  title = 'Jobs',
  itemCount = 6,
  className,
  noTopBottomPadding,
  ItemSkeleton,
}) => {
  const skeletons = Array(itemCount).fill(0);

  return (
    <Flex
      className={cnj(
        classes.wrapper,
        noTopBottomPadding && classes.noTopBottomPadding,
        className?.root
      )}
    >
      {title && (
        <SearchListHeader
          title={title}
          subTitle=""
          isLoading
          className={className?.header}
        />
      )}
      {skeletons.map((_, index) =>
        ItemSkeleton ? (
          <ItemSkeleton key={`${entity}_skeleton_${index}`} />
        ) : (
          <SearchListItemSkeleton
            key={`${entity}_skeleton_${index}`}
            type={entity}
          />
        )
      )}
    </Flex>
  );
};

export default SearchListSkeleton;
