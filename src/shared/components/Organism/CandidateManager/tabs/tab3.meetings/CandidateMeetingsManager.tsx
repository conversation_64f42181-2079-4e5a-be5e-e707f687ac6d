import { useCallback, useState } from 'react';
import { MeetingCard } from '@shared/components/molecules/MeetingCard';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { useSchedulesCalendar } from '@shared/hooks/useSchedulesCalendar';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import {
  MeetingDatetimeType,
  MeetingTabs,
} from '@shared/types/schedules/schedules';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import {
  getUpcomingMeetings,
  getPastMeetings,
} from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { ORIGINAL_CANDIDATE } from '@shared/components/Organism/CandidateManager/contants';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { dayjs } from '@shared/utils/Time';
import { useManagerContext } from '../../CandidateManager.context';
import { FooterWrap } from '../../components/FooterWrap';
import classes from './CandidateMeetingsManager.module.scss';
import type { CandidateFormData } from '@shared/types/candidates';

interface CandidateDocumentsManagerProps {
  candidate: CandidateFormData;
}

const cardWrapperClassNames = {
  root: '!p-8 !gap-8 rounded bg-background border border-solid border-techGray_20',
  titleWrapper: '!justify-between',
};

export function CandidateMeetingsManager({
  candidate,
}: CandidateDocumentsManagerProps) {
  const { t } = useTranslation();
  const { openCreateEventWithDate } = useSchedulesCalendar();
  const [activeTab, setActiveTab] = useState<MeetingTabs>(MeetingTabs.UPCOMING);
  const { setScheduleEventsPanelData } = useSchedulesUrlState();
  const { selectedSummary } = useManagerContext();
  const isCandidateMode = selectedSummary?.type === ORIGINAL_CANDIDATE;

  const infiniteQuery = useReactInfiniteQuery(
    [
      QueryKeys.candidateMeetings,
      isCandidateMode ? selectedSummary?.candidateId : selectedSummary?.id,
      activeTab,
    ],
    {
      func: (props: { pageParam?: number }) =>
        activeTab === MeetingTabs.UPCOMING
          ? getUpcomingMeetings({
              params: {
                candidate: selectedSummary?.candidateId,
                participationId: selectedSummary?.id,
                isCandidateMode,
                page: props.pageParam,
                size: 10,
              },
            })
          : getPastMeetings({
              params: {
                candidate: selectedSummary?.candidateId,
                participationId: selectedSummary?.id,
                isCandidateMode,
                page: props.pageParam,
                size: 10,
              },
            }),
      size: 10,
    },
    {
      enabled: !!(selectedSummary?.candidateId || selectedSummary?.id),
      refetchOnWindowFocus: false,
    }
  );

  const meetings = infiniteQuery.data;

  const handleCreateEvent = useCallback(() => {
    openCreateEventWithDate(
      dayjs().add(1, 'day'),
      {
        schedulesEventType: ScheduleEventTypes.MEETING,
        targetAttendee: {
          ...candidate.profile,
          id: candidate.profile.originalId,
        },
        datetimeType: MeetingDatetimeType.PROVIDE_AVAILABILITY,
      },
      {
        isInCandidateManager: true,
        candidateId: selectedSummary?.candidateId,
        participationId: selectedSummary?.id,
        isCandidateMode,
        activeTab,
      }
    );
  }, [
    candidate.profile,
    openCreateEventWithDate,
    isCandidateMode,
    selectedSummary,
  ]);

  return (
    <>
      <Flex flexDir="row" className={classes.tabsContainer}>
        <Button
          className={cnj(
            classes.tabButton,
            activeTab === MeetingTabs.UPCOMING && classes.activeTab
          )}
          label={t('upcoming_meetings')}
          onClick={() => setActiveTab(MeetingTabs.UPCOMING)}
        />
        <Button
          className={cnj(
            classes.tabButton,
            activeTab === MeetingTabs.PAST && classes.activeTab
          )}
          label={t('past_meetings')}
          onClick={() => setActiveTab(MeetingTabs.PAST)}
        />
      </Flex>
      <Flex className={cnj(classes.meetingsContainer, classes.scrollArea)}>
        {infiniteQuery.isLoading ? (
          <Skeleton className={cnj(cardWrapperClassNames.root, 'h-32')} />
        ) : meetings.length > 0 ? (
          meetings.map((meeting) => (
            <MeetingCard
              key={`meeting_${meeting.id}`}
              actionProps={{
                label: t('view_meeting'),
                onClick: () =>
                  setScheduleEventsPanelData({
                    eventId: meeting.id,
                    isFromNotification: true,
                    isInCandidateManager: true,
                    isCandidateMode,
                    candidateId: selectedSummary?.candidateId,
                    participationId: selectedSummary?.id,
                    activeTab,
                    isInCrEdit: false,
                    schedulesEventType: ScheduleEventTypes.MEETING,
                  }),
              }}
              item={{ meeting }}
              cardProps={{
                classNames: { root: classes.jobItem },
              }}
            />
          ))
        ) : (
          <EmptySearchResult
            className={classes.emptyResult}
            title={t('no_meeting_found')}
            sectionMessage={t('no_meeting_found_desc')}
          />
        )}
      </Flex>
      <FooterWrap>
        <Button
          className={classes.createButton}
          label={t('create_meeting')}
          leftIcon="plus"
          leftSize={16}
          onClick={handleCreateEvent}
        />
      </FooterWrap>
    </>
  );
}
