import type { PipelineAutoTodoResponse } from '@shared/utils/api/pipeline';

export function transformAssigneeUser(freshTodo?: PipelineAutoTodoResponse) {
  if (!freshTodo?.assigneeUser) return null;

  const user = freshTodo.assigneeUser;

  return {
    label: `${user.name} ${user.surname}`,
    value: user.id,
    image: user.croppedImageUrl,
    job: user.occupationName,
    username: user.username ? `@${user.username}` : '',
    isPrivate: !user.allowPageRoleAssign,
    id: user.id,
  };
}
