import { useMutation } from '@tanstack/react-query';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import {
  deleteAutoMovement,
  getAutoMovement,
  updateAutoMovement,
} from '@shared/utils/api/pipeline';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import AutoConditionalModal from '../AutoConditionalModal/AutoConditionalModal';

export function AutoMoveRequirementsModal() {
  const automationState = useMultiStepFormState('automation');
  const pipelineId = Number((automationState?.data as any)?.id);
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();

  const {
    data: autoMovementData,
    isLoading: isLoadingAutoMovement,
    refetch: refetchAutoMovement,
  } = useReactQuery({
    action: {
      apiFunc: () => getAutoMovement(pipelineId),
      key: [QueryKeys.getAutoMovement, pipelineId],
    },
  });

  const { mutateAsync: updateAutoMovementMutation } = useMutation({
    mutationFn: updateAutoMovement,
    onSuccess: () => {
      handleSuccess({
        message: t('requirements_set_on_auto_move_subtitle'),
        title: t('requirements_set_on_auto_move'),
      });
      refetchAutoMovement();
    },
  });

  const {
    mutateAsync: deleteAutoMovementMutation,
    isPending: isLoadingDeleteAutoMovement,
  } = useMutation({
    mutationFn: deleteAutoMovement,
    onSuccess: () => {
      refetchAutoMovement();
    },
  });

  return (
    <AutoConditionalModal
      autoConditionData={autoMovementData}
      isLoadingAutoCondition={isLoadingAutoMovement}
      updateAutoConditional={updateAutoMovementMutation}
      deleteAutoConditionalMutation={deleteAutoMovementMutation}
      isLoadingDeleteAutoConditional={isLoadingDeleteAutoMovement}
      automationType="autoMove"
      headerTitle={t('move_to')}
    />
  );
}
