import React from 'react';
import { AutomationStageType } from '@shared/components/Organism/AutomationModal/components/AutomationStageType';
import {
  useActionOptions,
  useTypeOptions,
} from '@shared/components/Organism/AutomationModal/components/commonOptions';
import { TwoButtonFooter } from '@shared/components/Organism/MultiStepForm/ProfileSections/Components/TwoButtonFooter';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import AutoComplete from '@shared/uikit/AutoComplete';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Typography from '@shared/uikit/Typography';
import useMedia from '@shared/uikit/utils/useMedia';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import useTranslation from '@shared/utils/hooks/useTranslation';
import geoApi from 'shared/utils/api/geo';
import type { MultiStepFormProps } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import type { PipelineInfo } from '@shared/types/pipelineProps';

interface ActionOption {
  value: string;
  label: string;
}

interface StatusOption {
  value: string;
  label: string;
}

interface RequirementBox {
  id: string;
  action: ActionOption | null;
  status: StatusOption | null;
  value: any;
}

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  getStepHeaderProps?: Exclude<
    MultiStepFormProps['getStepHeaderProps'],
    undefined
  >;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

interface UseAutoConditionalStepOneProps {
  onFormDataChange: (formData: any) => void;
  automationType: 'autoMove' | 'autoReject';
  headerTitle: string;
}

export function useAutoConditionalStepOne({
  onFormDataChange,
  automationType,
  headerTitle,
}: UseAutoConditionalStepOneProps): SingleDataItem[] {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const actionOptions = useActionOptions();
  const typeOptions = useTypeOptions();
  const { data } = useMultiStepFormState('automation') as {
    data: PipelineInfo | undefined;
  };
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const conditionMatchItems = [
    {
      label: t('all_requirements_must_be_met'),
      value: 'ALL',
    },
    {
      label: t('one_requirement_can_be_met'),
      value: 'ANY',
    },
  ];

  const LOCATION_FIELD = {
    apiFunc: geoApi.searchPlace,
    cp: 'asyncAutoCompleteWithExtraParams',
    label: t('location'),
    name: 'locationWithExtraParams',
    autoComplete: 'nope',
    rightIconProps: {
      name: 'search',
    },
    params: {
      countryCode,
    },
    visibleRightIcon: true,
  };

  const handleAddBox = (
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    const newBox: RequirementBox = {
      id: Date.now().toString(),
      action: null,
      status: null,
      value: null,
    };
    setFieldValue('requirementBoxes', [...requirementBoxes, newBox]);
  };

  const handleRemoveBox = (
    id: string,
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    setFieldValue(
      'requirementBoxes',
      requirementBoxes.filter((box) => box.id !== id)
    );
  };

  const handleActionChange = (
    id: string,
    action: ActionOption,
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    setFieldValue(
      'requirementBoxes',
      requirementBoxes.map((box) => (box.id === id ? { ...box, action } : box))
    );
  };

  const handleStatusChange = (
    id: string,
    status: StatusOption,
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    setFieldValue(
      'requirementBoxes',
      requirementBoxes.map((box) => (box.id === id ? { ...box, status } : box))
    );
  };

  const getFieldsForAction = (actionValue: string) => {
    switch (actionValue) {
      case 'location':
        return [LOCATION_FIELD];
      case 'age':
        return [
          {
            label: t('min_age'),
            name: 'minAge',
            type: 'number',
            cp: 'input',
            inputProps: {
              placeholder: t('min_age'),
            },
          },
          {
            label: t('max_age'),
            name: 'maxAge',
            type: 'number',
            cp: 'input',
            inputProps: {
              placeholder: t('max_age'),
            },
            wrapStyle: 'mt-20',
          },
        ];
      case 'coverLetter':
        return [];
      case 'phoneNumber':
        return [];
      default:
        return [];
    }
  };

  const renderValueField = (box: RequirementBox, _formikProps: any) => {
    if (!box.action || !box.status) return null;

    const fields = getFieldsForAction(box.action.value);
    if (fields.length === 0) return null;

    const fieldsWithBoxId = fields.map((field) => ({
      ...field,
      name: `requirementBox_${box.id}_${field.name}`,
    }));

    return (
      <Flex className="gap-12">
        <Typography className="!text-primaryText !text-base !font-bold">
          {t('requirment')}
        </Typography>
        <DynamicFormBuilder groups={fieldsWithBoxId} />
      </Flex>
    );
  };

  const transformFormData = (values: any) => {
    const requirementBoxes = values.requirementBoxes || [];
    const selectedConditionMatch =
      values.selectedConditionMatch || conditionMatchItems[0];

    const ageBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'age'
    );
    const locationBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'location'
    );
    const coverLetterBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'coverLetter'
    );
    const phoneNumberBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'phoneNumber'
    );

    const getBoxValue = (
      box: RequirementBox | undefined,
      fieldName: string
    ) => {
      if (!box) return undefined;

      return values[`requirementBox_${box.id}_${fieldName}`];
    };

    const formValue = {
      toPipelineId: parseInt(data?.id || '0', 10),
      coverLetterCheckingEnabled:
        !!coverLetterBox && coverLetterBox.status?.value === 'is',
      phoneNumberCheckingEnabled:
        !!phoneNumberBox && phoneNumberBox.status?.value === 'is',
      ageRangeCheckingEnabled: !!ageBox,
      ageRangeIsBetweenOrNot: ageBox?.status?.value === 'is',
      minAge: getBoxValue(ageBox, 'minAge')
        ? parseInt(getBoxValue(ageBox, 'minAge'), 10)
        : 0,
      maxAge: getBoxValue(ageBox, 'maxAge')
        ? parseInt(getBoxValue(ageBox, 'maxAge'), 10)
        : 0,
      selectedConditionMatch,
      locationCheckingEnabled:
        !!locationBox && locationBox.status?.value === 'is',
      locationCountryCodeIsEqualsOrNot: locationBox?.status?.value === 'is',
      locationCountryCodeIs:
        getBoxValue(locationBox, 'locationWithExtraParams')?.countryCode || '',
      requirementBoxes,
    };

    return formValue;
  };

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('requirements'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => {
        closeMultiStepForm('automation');
        openMultiStepForm({
          formName: 'automation',
          data,
          type: automationType,
        });
      },
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({
    setStep,
    values,
  }) => {
    const requirementBoxes = values.requirementBoxes || [];

    return (
      <TwoButtonFooter
        submitLabel={t('next')}
        secondaryButtonLabel={t('discard')}
        onSubmitClick={() => {
          const formData = transformFormData(values);

          onFormDataChange(formData);
          setStep((prev) => prev + 1);
        }}
        disabledSubmit={
          !requirementBoxes?.some((box: RequirementBox) => {
            if (
              box.action?.value === 'coverLetter' ||
              box.action?.value === 'phoneNumber'
            ) {
              return box.action && box.status;
            }

            if (box.action && box.status) {
              const fields = getFieldsForAction(box.action.value);
              if (fields.length === 0) return true;

              return fields.some((field) => {
                const fieldName = `requirementBox_${box.id}_${field.name}`;

                return values?.[fieldName];
              });
            }

            return false;
          })
        }
        secondaryButtonOnClick={() => {
          closeMultiStepForm('automation');
        }}
      />
    );
  };

  const getStepHeaderProps: SingleDataItem['getStepHeaderProps'] = () => ({
    title: headerTitle,
    iconProps: {
      name: 'Pipe-move',
      type: 'fal',
    },
  });

  const renderBody: SingleDataItem['renderBody'] = ({
    values,
    setFieldValue,
  }) => {
    const requirementBoxes = values.requirementBoxes || [];
    const selectedConditionMatch =
      values.selectedConditionMatch || conditionMatchItems[0];

    return (
      <Flex className="h-full bg-darkSecondary overflow-y-auto">
        <Flex className="gap-20">
          <Flex className="gap-20">
            <AutomationStageType type={automationType} />

            <AutoComplete
              editable={false}
              visibleRightIcon
              variant="simple-large"
              value={selectedConditionMatch}
              onChangeInput={(value: any) => {
                const option = conditionMatchItems.find(
                  (opt) => opt.value === value.value
                );
                if (option && setFieldValue) {
                  setFieldValue('selectedConditionMatch', option);
                }
              }}
              inputWrapClassName="w-full"
              options={conditionMatchItems}
              renderItem={({ item }) => (
                <Flex
                  flexDir="row"
                  alignItems="center"
                  className="w-full h-[56px] gap-10"
                >
                  <Typography size={16}>{item.label}</Typography>
                </Flex>
              )}
              className="w-full"
              optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
              displayName={selectedConditionMatch.label}
              onSelect={(item: any) => {
                if (setFieldValue) {
                  setFieldValue('selectedConditionMatch', item);
                }
              }}
            />
          </Flex>

          {!!requirementBoxes?.length && (
            <Flex className="gap-20">
              {requirementBoxes?.map((box: RequirementBox) => (
                <Flex
                  key={box.id}
                  className="gap-20 bg-gray_5 p-20 border border-solid border-techGray_20 rounded-xl"
                >
                  <Flex
                    flexDir="row"
                    className="justify-between items-center w-full"
                  >
                    <Typography className="!text-xl !font-bold !text-smoke_coal">
                      {t('if')}
                    </Typography>
                    <IconButton
                      name="trash"
                      type="fas"
                      size="md20"
                      colorSchema="transparent"
                      iconProps={{
                        color: 'smoke_coal',
                      }}
                      onClick={() =>
                        handleRemoveBox(box.id, setFieldValue, requirementBoxes)
                      }
                      className=""
                    />
                  </Flex>
                  <Flex flexDir="column" className="gap-20">
                    <AutoComplete
                      editable={false}
                      visibleRightIcon
                      variant="simple-large"
                      placeholder={t('action')}
                      value={box.action ?? { value: '', label: '' }}
                      onSelect={(value: any) =>
                        handleActionChange(
                          box.id,
                          value,
                          setFieldValue,
                          requirementBoxes
                        )
                      }
                      inputWrapClassName="w-full"
                      options={actionOptions}
                      displayName={box.action?.label}
                      rightIconClassName=""
                      className="w-full"
                      optionsVariant={
                        isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                      }
                    />
                    {box.action && (
                      <AutoComplete
                        editable={false}
                        visibleRightIcon
                        variant="simple-large"
                        placeholder={t('status')}
                        value={box.status ?? { label: '', value: '' }}
                        onSelect={(value: any) =>
                          handleStatusChange(
                            box.id,
                            value,
                            setFieldValue,
                            requirementBoxes
                          )
                        }
                        inputWrapClassName="w-full"
                        displayName={box.status?.label}
                        options={typeOptions}
                        rightIconClassName=""
                        className="w-full"
                        optionsVariant={
                          isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                        }
                      />
                    )}
                    {box.action &&
                      box.status &&
                      renderValueField(box, { values, setFieldValue })}
                  </Flex>
                </Flex>
              ))}
            </Flex>
          )}
          {requirementBoxes?.length <= 3 && (
            <Flex flexDir="row" className="justify-start">
              <IconButton
                name="plus"
                type="far"
                size="md"
                colorSchema="graySecondary"
                variant="circle"
                onClick={() => handleAddBox(setFieldValue, requirementBoxes)}
                tooltip={t('add_requirement')}
              />
            </Flex>
          )}
        </Flex>
      </Flex>
    );
  };

  const stepData: Array<SingleDataItem> = [
    {
      stepKey: '1',
      getHeaderProps,
      getStepHeaderProps,
      renderBody,
      renderFooter,
    },
  ];

  return stepData;
}
