import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import {
  mapAttachmentsToFileIds,
  mapFileIdsToAttachments,
} from '@shared/components/Organism/AutomationModal/components/utils';
import useResponseToast from '@shared/hooks/useResponseToast';
import {
  putPipelineAutoNote,
  deletePipelineAutoNote,
  getPipelineAutoNote,
} from '@shared/utils/api/pipeline';
import { QueryKeys } from '@shared/utils/constants';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  PipelineAutoNote,
  PipelineAutoNoteRequest,
  PipelineAutoNoteResponse,
} from '@shared/utils/api/pipeline';

interface FormValues {
  visibility: 'EVERYONE' | 'ONLY_ME';
  text: string;
  attachments: Array<{ id: string }>;
}

interface UseNoteStateProps {
  isOpen: boolean;
  pipelineId: number;
}

export const useNoteState = ({ isOpen, pipelineId }: UseNoteStateProps) => {
  const { handleSuccess } = useResponseToast();
  const { t } = useTranslation();
  const [editMode, setEditMode] = useState(false);

  const queryClient = useQueryClient();

  const {
    data: note,
    isLoading,
    refetch,
  } = useReactQuery<PipelineAutoNote | null>({
    action: {
      key: [QueryKeys.pipelineAutoNote, pipelineId],
      apiFunc: () => getPipelineAutoNote(pipelineId),
    },
    config: {
      enabled: isOpen && !!pipelineId,
      refetchOnMount: false,
      onSuccess: (data) => {
        if (data) {
          setEditMode(false);
        } else if (isOpen && pipelineId) {
          setEditMode(true);
        }
      },
    },
  });

  const updateMutation = useReactMutation<
    PipelineAutoNoteResponse,
    PipelineAutoNoteRequest
  >({
    apiFunc: (body) => putPipelineAutoNote(pipelineId, body),
    mutationKey: [QueryKeys.pipelineAutoNoteUpdate, pipelineId],
    onSuccess: () => {
      handleSuccess({
        message: t('note_saved_title'),
        title: t('note_saved_subtitle'),
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.pipelineAutoNote, pipelineId],
      });
    },
  });

  const deleteMutation = useReactMutation<void>({
    apiFunc: () => deletePipelineAutoNote(pipelineId),
    mutationKey: [QueryKeys.pipelineAutoNoteDelete, pipelineId],
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.pipelineAutoNote, pipelineId],
      });
    },
  });

  const getInitialFormValues = (): FormValues => ({
    visibility: note?.visibility || 'EVERYONE',
    text: note?.text || '',
    attachments: mapFileIdsToAttachments(note?.fileIds ?? []),
  });

  const handleUpdate = async (values: FormValues) => {
    if (!pipelineId) return;

    const body: PipelineAutoNoteRequest = {
      visibility: values.visibility,
      text: values.text,
      fileIds: mapAttachmentsToFileIds(values.attachments || []),
    };
    await updateMutation.mutateAsync(body);
    setEditMode(false);
  };

  const handleDelete = async (resetForm: () => void) => {
    if (!pipelineId) return;

    await deleteMutation.mutateAsync(undefined);
    setEditMode(true);
    resetForm();
  };

  const setEditModeTrue = async (resetForm: (values: FormValues) => void) => {
    // const result = await refetch();
    const freshNote = note;
    if (freshNote) {
      resetForm({
        visibility: freshNote.visibility || 'EVERYONE',
        text: freshNote.text || '',
        attachments: mapFileIdsToAttachments(freshNote.fileIds ?? []),
      });
    }
    setEditMode(true);
  };

  const isMutating = updateMutation.isPending || deleteMutation.isPending;

  return {
    note,
    user: note?.user,
    pipeline: note?.pipeline,
    editMode,
    setEditMode,
    isLoading,
    handleUpdate,
    isMutating,
    handleDelete,
    setEditModeTrue,
    getInitialFormValues,
    updateMutation,
    deleteMutation,
  };
};
