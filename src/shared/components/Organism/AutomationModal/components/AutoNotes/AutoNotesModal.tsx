import React from 'react';
import { AutomationModalWrapper } from '@shared/components/Organism/AutomationModal/components/AutomationModalWrapper';
import { NoteDisplay } from '@shared/components/Organism/AutomationModal/components/AutoNotes/NoteDisplay';
import { NoteForm } from '@shared/components/Organism/AutomationModal/components/AutoNotes/NoteForm';
import { useNoteState } from '@shared/components/Organism/AutomationModal/components/AutoNotes/useNoteState';
import { transformNote } from '@shared/components/Organism/AutomationModal/utils';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import Button from '@shared/uikit/Button';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import useTranslation from '@shared/utils/hooks/useTranslation';

const AutoNotesModal: React.FC = () => {
  const automationState = useMultiStepFormState('automation');
  const pipelineId = Number((automationState?.data as any)?.id);
  const isOpen = automationState?.isOpen || false;

  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });

  const {
    note,
    editMode,
    setEditMode,
    isMutating,
    handleUpdate,
    handleDelete,
    setEditModeTrue,
    getInitialFormValues,
  } = useNoteState({ isOpen, pipelineId });

  const { t } = useTranslation();

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'main',
    });
  };

  const renderFooter = (formikProps: any) => {
    const { values, isValid, resetForm, setValues, isSubmitting } = formikProps;
    if (editMode || !note) {
      return (
        <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0 w-full">
          <Button
            label={t('discard')}
            schema="gray"
            variant="default"
            onClick={() => {
              if (note) {
                setEditMode(false);
                setValues({
                  visibility: note.visibility,
                  text: note.text,
                  attachments: (note.fileIds || []).map((id: string) => ({
                    id: String(id),
                  })),
                });
              } else {
                resetForm();
              }
            }}
            className="flex-1"
          />
          <Button
            label={note ? t('update') : t('create')}
            schema="primary-blue"
            variant="default"
            onClick={() => handleUpdate(values)}
            className="flex-1"
            disabled={isMutating || isSubmitting || !isValid}
            isLoading={isMutating || isSubmitting}
          />
        </Flex>
      );
    }

    return (
      <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0 w-full">
        <Button
          label={t('delete')}
          schema="transparent"
          variant="default"
          onClick={() => {
            openConfirmDialog({
              title: t('delete_note') || t('delete'),
              message:
                t('are_you_sure_you_want_to_delete_this_auto') ||
                t('are_you_sure_want_remove'),
              confirmButtonText: t('delete'),
              cancelButtonText: t('cancel'),
              isAjaxCall: true,
              apiProps: {
                func: () => handleDelete(resetForm),
                onSuccess: () => setEditModeTrue(setValues),
              },
            });
          }}
          className="flex-1"
          leftIcon="trash"
          disabled={isMutating || isSubmitting}
          isLoading={isMutating || isSubmitting}
        />
        <Button
          label={t('edit')}
          schema="semi-transparent"
          variant="default"
          leftIcon="edit"
          onClick={() => setEditModeTrue(setValues)}
          className="flex-1"
        />
      </Flex>
    );
  };

  const getModalTitle = () => {
    if (!note) return t('create_note');

    return editMode ? t('edit_note') : t('note');
  };

  return (
    <Form
      initialValues={getInitialFormValues()}
      onSuccess={handleUpdate}
      enableReinitialize
      local
    >
      {(formikProps) => (
        <AutomationModalWrapper
          isOpen={isOpen}
          onClose={handleClose}
          title={getModalTitle()}
          backButtonProps={{ onClick: handleClose }}
          noCloseButton
          hideBack={false}
          footer={renderFooter(formikProps)}
        >
          {editMode || !note ? <NoteForm /> : <NoteDisplay note={note} />}
        </AutomationModalWrapper>
      )}
    </Form>
  );
};

export default AutoNotesModal;
