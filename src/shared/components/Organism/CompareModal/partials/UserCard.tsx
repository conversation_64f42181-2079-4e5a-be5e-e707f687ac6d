import { type MouseEvent } from 'react';
import ObjectInfoCard from '@shared/components/molecules/ObjectInfoCard';
import Avatar from '@shared/uikit/Avatar';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import classes from './index.module.scss';
import type { CompareVariant } from '@shared/components/Organism/CompareModal/index';

interface UserCardProps {
  user?: any;
  setOpenCandidates?: (value?: boolean) => void;
  onRemove?: (id: string) => (event?: MouseEvent<any>) => void;
  variant: CompareVariant;
}

const UserCard = (props: UserCardProps) => {
  const { user, setOpenCandidates, onRemove } = props;
  const { t } = useTranslation();
  if (user)
    return (
      <Flex className={classes.cardWrapper}>
        {props?.variant === 'editable' && (
          <IconButton
            className={classes.iconWrapper}
            name="times"
            colorSchema="primary"
            onClick={onRemove?.(user.id)}
          />
        )}
        <Avatar
          imgSrc={user?.croppedImageUrl}
          bordered={false}
          isCompany={false}
          size="flg"
          className={classes.avatarWrapper}
        />
        <ObjectInfoCard
          firstText={user?.profile?.fullName}
          secondText={user?.profile?.usernameAtSign}
          thirdText={user?.profile?.occupation?.label}
          fourthText={cleanRepeatedWords(user?.profile?.location?.title || '')}
          withAvatar={false}
          className="w-full"
        />
      </Flex>
    );
  if (props?.variant === 'readonly') return null;

  return (
    <Flex
      className={cnj(classes.cardWrapper, classes.emptyCard)}
      // className="justify-center items-center gap-10 p-16 w-full"
      onClick={() => setOpenCandidates?.(true)}
    >
      <Icon name="plus" type="far" size={28} color="secondaryDisabledText" />
      <Typography size={15} font="700" height={18} color="colorIconForth2">
        {t('add_candidate')}
      </Typography>
    </Flex>
  );
};

export default UserCard;
