import { useMemo } from 'react';
import Button from '@shared/uikit/Button';
import CheckBox from '@shared/uikit/CheckBox';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import AvatarCard from 'shared/uikit/AvatarCard';
import type { CandidateFormData } from '@shared/types/candidates';

interface ILinkJobModalCandidateItemProps {
  candidate: CandidateFormData;
  onClick: (candidate: CandidateFormData, action: 'add' | 'remove') => void;
  selectedCandidates: CandidateFormData[];
}

const LinkJobModalCandidateItem: React.FunctionComponent<
  ILinkJobModalCandidateItemProps
> = (props) => {
  const { candidate, onClick, selectedCandidates } = props;
  const { t } = useTranslation();
  const isSelected = useMemo(
    () =>
      selectedCandidates?.some((sc) => String(sc.id) === String(candidate.id)),
    [selectedCandidates, candidate]
  );

  return (
    <AvatarCard
      onClick={() => onClick(candidate, isSelected ? 'remove' : 'add')}
      avatarProps={{
        imgSrc: candidate.profile.croppedImageUrl,
        bordered: false,
        isCompany: false,
        name: candidate.profile.fullName,
      }}
      data={{
        title: candidate.profile.fullName,
        subTitle: (
          <Typography color="secondaryDisabledText" size={14} height={16}>
            {candidate.profile?.usernameAtSign ||
              candidate?.profile?.email?.value}
          </Typography>
        ),
      }}
      action={
        <Flex className="!flex-row gap-8 ml-auto items-center">
          <a
            href={`/search/candidates?currentEntityId=${candidate.id}`}
            target="_blank"
          >
            <Button label={t('view_details')} schema="semi-transparent" />
          </a>

          <CheckBox
            classNames={{ wrapper: 'ml-auto' }}
            value={isSelected ? candidate : undefined}
          />
        </Flex>
      }
      containerProps={{ className: '!-mx-8 !mb-4' }}
    />
  );
};

export default LinkJobModalCandidateItem;
