import React, { type FC } from 'react';
import { type MultiStepModalProps } from '@shared/hooks/useMultiStepForm';
import { type PaymentRequestType } from '@shared/types/page';
import { type FixedRightSideModalDialogProps } from '@shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import { type Billing } from '../../Billing/types';
import CheckoutForm from './CheckoutModal.form';
import CheckoutResult from './CheckoutResult';

function isModalOpen<PropTypes>(
  data: MultiStepModalProps<PropTypes>
): data is { isOpen: true } & PropTypes {
  return data.isOpen;
}

type BaseInvoiceType = {
  price: string | number;
  requestType: PaymentRequestType;
  taxAmount: string | number;
  numberOfSeats?: string | number;
  pricePerSeat?: string;
  id?: string;
  discount?: string | number;
  enrollParams?: any;
};

type CheckoutEntityDataType = {
  label: string;
  subTitle?: string;
  priceUnit?: string;
  requestType: PaymentRequestType;
  Logo?: FC;
};

export type CheckoutChildModalData = {
  invoice: BaseInvoiceType;
  entityData: CheckoutEntityDataType;
  actions?: {
    onSuccess?: (response: any) => void;
    onError?: (response: any) => void;
    onFailure?: (response: any) => void;
    onCancel?: () => void;
    beforeSubmit?: (response: any) => void;
  };
  modalProps?: Partial<FixedRightSideModalDialogProps>;
};

export type PaymentResultData = {
  status: 'success' | 'error' | 'failure';
  isSuccess: boolean;
  billing?: Billing;
  title: string;
  message?: string;
  date?: string;
  hasError?: boolean;
  billingId?: string | number;
};

export type OpenCheckoutModalProps = {
  data: CheckoutChildModalData;
  options?: PaymentResultData;
};
export type CheckoutModalProps = MultiStepModalProps<OpenCheckoutModalProps>;

export default function CheckoutModal(props: CheckoutModalProps) {
  if (!isModalOpen(props)) return null;
  const { data, options } = props;

  if (options) return <CheckoutResult {...options} {...data} />;

  return (
    <CheckoutForm
      isOpen
      invoice={data?.invoice}
      entityData={data?.entityData}
      actions={data?.actions}
    />
  );
}
