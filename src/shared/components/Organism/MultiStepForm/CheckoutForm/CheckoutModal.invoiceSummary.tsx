import React from 'react';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import { type IconProps } from '@shared/uikit/Icon/types';
import Tooltip, { type TooltipProps } from '@shared/uikit/Tooltip/Tooltip';
import Typography, { type TypographyProps } from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from './index.module.scss';
import { type CheckoutChildModalData } from '.';

export default function InvoiceSummary({
  price,
  discount = 0,
  taxAmount,
  pricePerSeat,
  numberOfSeats = 1,
}: CheckoutChildModalData['invoice']) {
  const { t } = useTranslation();

  return (
    <Flex className={classes.invoiceSummaryWrapper}>
      <InvoiceSummaryItem
        label={t('subtotal')}
        value={`${numberOfSeats} x ${pricePerSeat} = $${Number(numberOfSeats) * Number(pricePerSeat)}`}
      />
      <InvoiceSummaryItem
        label={t('discount')}
        value={`$${Number(discount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
      />
      {taxAmount && (
        <InvoiceSummaryItem
          label={t('tax')}
          value={`$${Number(taxAmount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
          hint={t('tax_amount_label_hint')}
          tooltipProps={{
            tooltipWrapperProps: {
              className: classes.hintTooltip,
            },
          }}
        />
      )}
      {price && (
        <InvoiceSummaryItem
          label={t('total')}
          value={`$${Number(price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
        />
      )}
    </Flex>
  );
}

type InvoiceSummaryItemProps = {
  label?: string;
  labelProps?: Partial<TypographyProps>;
  value?: string;
  valueProps?: Partial<TypographyProps>;
  wrapperClassName?: string;
  hint?: string;
  hintIconProps?: IconProps;
  tooltipProps?: Omit<TooltipProps, 'trigger' | 'children'>;
};

export function InvoiceSummaryItem({
  wrapperClassName,
  label,
  labelProps = {},
  value,
  valueProps = {},
  hint,
  hintIconProps = {},
  tooltipProps = {},
}: InvoiceSummaryItemProps) {
  return (
    <Flex className={cnj(classes.invoiceSummaryItemWrapper, wrapperClassName)}>
      <Typography
        size={16}
        font="500"
        height={22}
        {...labelProps}
        className={cnj(classes.labelWithHint, labelProps?.className)}
      >
        {label}
        {hint && (
          <Tooltip
            {...tooltipProps}
            trigger={
              <Icon
                name="info-circle"
                type="fal"
                size={16}
                {...hintIconProps}
              />
            }
          >
            {hint}
          </Tooltip>
        )}
      </Typography>
      <Typography
        size={15}
        font="500"
        height={22}
        color="secondaryDisabledText"
        {...valueProps}
      >
        {value}
      </Typography>
    </Flex>
  );
}
