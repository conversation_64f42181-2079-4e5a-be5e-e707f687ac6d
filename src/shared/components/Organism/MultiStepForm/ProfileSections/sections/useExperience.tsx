import React, { useMemo } from 'react';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import useProfilePage from 'shared/hooks/useProfilePage';
import Flex from 'shared/uikit/Flex';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import cnj from 'shared/uikit/utils/cnj';
import geoApi from 'shared/utils/api/geo';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';
import isEmptyObjectValues from 'shared/utils/toolkit/isEmptyObjectValues';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import { jobsDb, db } from 'shared/utils/constants/enums';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import {
  addDisplayDuration,
  experienceNormalizer,
} from '@shared/utils/experience.utils';
import useMedia from 'shared/uikit/utils/useMedia';
import { CombinedFooter } from '../Components/CombinedFooter';
import { ViewCard } from '../Components/ViewCard';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';
import { getHeaderProps } from './utils/getHeaderProps';
import { useUtils } from './utils/useUtils';

const useExperience = (isVolunteer = false): SingleDataItem => {
  const { t } = useTranslation();

  const { isMoreThanTablet } = useMedia();
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const { checkSectionVisibility } = useProfilePage();
  const visibleCurrentExperience = checkSectionVisibility(
    'visibleCurrentExperience'
  );
  const visiblePastExperience = checkSectionVisibility('visiblePastExperience');

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleClick,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    editUrl,
  } = useUtils({
    url: Endpoints.App.User[isVolunteer ? 'Volunteers' : 'Experiences'].get,
    highlightDataTransformer: (data) => ({
      ...data,
      profileEntityId: data.id,
      subTitle: data.companyName,
      title: data.occupationName,
      type: isVolunteer ? HIGHLIGHT_TYPES.VOLUNTEERING : HIGHLIGHT_TYPES.JOB,
    }),
  });

  const experiences = useMemo(
    () =>
      data?.[isVolunteer ? 'volunteers' : 'experiences']?.map((item) => ({
        ...item,
        companyPageId:
          item?.companyPageId ||
          `${item.companyName?.trim().toLowerCase()}_temp`,
      })),
    [data?.experiences, data?.volunteers]
  );

  const normalizedExperience = useMemo(() => {
    const temp = experiences
      ?.filter((i) => {
        if (visiblePastExperience && !i.currentlyWorking) {
          return true;
        }

        return !!(visibleCurrentExperience && i.currentlyWorking);
      })
      .reduce(experienceNormalizer, [])
      .map(addDisplayDuration);

    return temp
      ?.map((item: any) => {
        if (!item?.steps) return item;

        return item?.steps?.map((step: any) => ({
          ...step,
          image: item?.image,
        }));
      })
      ?.flat();
  }, [experiences, visibleCurrentExperience, visiblePastExperience]);

  const initialValues = {
    ...(activeItem || {}),
  };

  return {
    stepKey: isVolunteer
      ? profileSectionsStepKeys.VOL_EXPERIENCE
      : profileSectionsStepKeys.PRO_EXPERIENCE,
    url:
      activeState === 'edit'
        ? editUrl
        : Endpoints.App.User[isVolunteer ? 'Volunteers' : 'Experiences'].get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getExperienceValidationSchema,
    transform: transformExperience,
    getHeaderProps: ({ setStep, dirty }) =>
      getHeaderProps({
        dirty,
        setStep,
        activeState,
        handleBackWithConfirm: handleBackWithConfirm(),
        titles: isVolunteer
          ? {
              main: t('volunteers'),
              add: t('add_volunteer'),
              edit: t('edit_volunteer'),
            }
          : {
              main: t('experiences'),
              add: t('add_experience'),
              edit: t('edit_experience'),
            },
        isSingle,
      }),
    renderFooter: ({ setStep, dirty }) => (
      <CombinedFooter
        setStep={setStep}
        activeState={activeState}
        deleteWithConfirm={deleteWithConfirm(deleteItem)}
        dirty={dirty}
        handleBackWithConfirm={handleBackWithConfirm()}
        setActiveState={setActiveState}
        data={normalizedExperience}
        addLabel={isVolunteer ? t('add_volunteer') : t('add_experience')}
      />
    ),
    renderBody: ({ values }) => {
      if (activeState === 'list' && normalizedExperience?.length) {
        return (
          <Flex className={classes.listContainer}>
            {normalizedExperience?.map((item: any) => (
              <ViewCard
                item={{
                  id: item?.id,
                  image: item?.image,
                  firstText: item?.realData?.job?.label,
                  secondText: item?.realData?.company?.label,
                  thirdText: item?.secondText,
                  onClick: handleClick(normalizedExperience),
                }}
              />
            ))}
          </Flex>
        );
      }

      return (
        <DynamicFormBuilder
          className={classes.formRoot}
          groups={getExperienceGroups({
            t,
            isMoreThanTablet,
            isVolunteer,
            values,
            countryCode,
          })}
        />
      );
    },
  };
};

export default useExperience;

export function getExperienceGroups({
  t,
  isMoreThanTablet,
  isVolunteer,
  values,
  isHighlight = false,
  disabledReadOnly,
  isEdit = false,
  countryCode,
}: any) {
  return [
    {
      name: 'job',
      cp: 'asyncAutoComplete',
      disabledReadOnly,
      maxLength: 100,
      label: t('job_title'),
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizer,
      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      wrapStyle: cnj(classes.formItem),
    },
    {
      name: 'company',
      cp: 'avatarAsyncAutoComplete',
      disabledReadOnly,
      isCompany: true,
      maxLength: 100,
      params: {
        countryCode,
      },
      label: t('company'),
      wrapStyle: cnj(classes.formItem),
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      name: 'location',
      apiFunc: geoApi.suggestCity,
      label: t('location'),
      wrapStyle: cnj(classes.formItem),
      visibleRightIcon: true,
      rightIconProps: {
        name: 'search',
      },
      cp: 'asyncAutoCompleteWithExtraParams',
      disabledReadOnly,
      privateable: false,
      onlyChooseFromOptions: true,
    },
    {
      name: 'workPlaceType',
      cp: 'dropdownSelect',
      disabledReadOnly,
      label: t('workspace_type'),
      wrapStyle: classes.formItem,
      options: jobsDb.WORK_SPACE_MODEL,
    },
    isVolunteer
      ? {
          name: 'cause',
          cp: 'dropdownSelect',
          disabledReadOnly,
          label: t('cause'),
          wrapStyle: cnj(classes.formItem),
          options: db.CAUSE_OPTIONS,
        }
      : {
          name: 'employmentType',
          cp: 'dropdownSelect',
          disabledReadOnly,
          label: t('contract_type'),
          wrapStyle: cnj(classes.formItem),
          options: db.EMPLOYMENT_TYPES,
        },
    {
      name: 'currentlyWorking',
      cp: 'checkBox',
      // TODO: `disabledReadOnly` props is not working on checkbox
      disabledReadOnly,
      disabled: disabledReadOnly,
      label: t('currently_working_at_this_position'),
      wrapStyle: cnj(classes.formItem),
      visibleOptionalLabel: false,
    },
    {
      name: 'startDate',
      cp: 'datePicker',
      disabledReadOnly,
      wrapStyle: cnj(classes.formItem, isMoreThanTablet && classes.grow),
      variant: 'input',
      maxDate: new Date(),
      required: true,
      picker: 'month',
      label: t('start_date'),
      isFirstHalfWidth: isMoreThanTablet,
      rowContainerClassName: classes.rowContainerClassName,
      halfWidthWhenOpen: true,
    },
    {
      name: 'endDate',
      cp: 'datePicker',
      wrapStyle: cnj(classes.formItem, isMoreThanTablet && classes.grow),
      variant: 'input',
      picker: 'month',
      minDate: values?.startDate ? new Date(values?.startDate) : undefined,
      maxDate: new Date(),
      label: t('end_date'),
      visibleOptionalLabel: false,
      required: false,
      disabledReadOnly,
      textInputProps: {
        disabled: values?.currentlyWorking,
      },
      isSecondHalfWidth: isMoreThanTablet,
      halfWidthWhenOpen: true,
    },
    {
      name: 'description',
      cp: 'richtext',
      disabledReadOnly,
      label: t('description'),
      wrapStyle: cnj(classes.formItem, classes.grow),
      maxLength: DESCRIPTION_MAX_LENGTH,
      visibleOptionalLabel: true,
      showEmoji: false,
      className: classes.growingDescription,
    },
  ]?.reduce((prev: any, curr) => {
    if (isHighlight && curr.name === 'description') {
      return prev;
    }
    if (curr.name === 'share' && isEdit) {
      return prev;
    }

    return [...prev, curr];
  }, []);
}

export function getExperienceValidationSchema() {
  return formValidator.object().shape({
    location: formValidator
      .object()
      .test(
        'value',
        'select_one_of_sug_locns',
        (val: any) => isEmptyObjectValues(val) || val?.value
      ),
    endDate: formValidator
      .date()
      .typeError('this_field_is_required')
      .when(
        ['startDate', 'currentlyWorking'],
        (startDate, currentlyWorking, schema) => {
          if (currentlyWorking) {
            return schema.nullable();
          }

          return startDate
            ? schema
                .min(startDate, 'date_e_b_s')
                .required('this_field_is_required')
            : schema.nullable();
        }
      ),
    description: descriptionLengthValidator,
  });
}

export const transformExperience = ({
  job,
  employmentType,
  company,
  endDate,
  currentlyWorking,
  cause,
  description,
  workPlaceType,
  ...rest
}: any) => ({
  ...rest,
  currentlyWorking,
  workPlaceType: workPlaceType?.value,
  endDate: currentlyWorking ? undefined : endDate,
  occupationLookupId: job?.value,
  occupationName: job?.label,
  employmentType: employmentType?.value,
  companyPageId: `${company?.value}`?.includes?.('_temp')
    ? null
    : company?.value,
  pageCroppedImageUrl: company?.image,
  companyName: company?.label,
  cause: cause?.value,
  description: removeBreaksAndSpaces(description),
});
