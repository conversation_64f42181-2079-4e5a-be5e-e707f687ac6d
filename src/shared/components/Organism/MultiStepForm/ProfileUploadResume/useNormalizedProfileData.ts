import { useMemo } from 'react';
import {
  addDisplayDuration,
  experienceNormalizer,
  safeCutDescription,
} from '@shared/utils/experience.utils';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { normalizersMapper } from './normalizersMapper';

export function useNormalizedProfileData() {
  const { data: extractedProfileData } = useMultiStepFormState('resumeUpload');
  const { t } = useTranslation();

  const normalizedInitialData = useMemo(() => {
    const obj: any = extractedProfileData || {};

    const res: any = {};
    Object.keys(obj)?.map((key) => {
      if (['experiences', 'volunteers']?.includes(key)) return;
      res[key] = normalizersMapper[key]
        ? obj[key]?.map((item) => normalizersMapper[key](item, t))
        : obj[key];
      if (Array.isArray(res[key]) && !['skills', 'languages'].includes(key))
        res[key] = res[key]?.map((item) => item?.realData || item);
    });

    function getExperiences(_obj: any, isVolunteer: boolean) {
      const visiblePastExperience = true;
      const visibleCurrentExperience = true;
      const experiences = _obj?.[
        isVolunteer ? 'volunteers' : 'experiences'
      ]?.map((item) => ({
        ...safeCutDescription(item),
        companyPageId:
          item?.companyPageId ||
          `${item.companyName?.trim().toLowerCase()}_temp`,
      }));

      const temp = experiences
        ?.filter((i) => {
          if (visiblePastExperience && !i.currentlyWorking) {
            return true;
          }

          return !!(visibleCurrentExperience && i.currentlyWorking);
        })
        .reduce(experienceNormalizer, [])
        .map(addDisplayDuration);

      return temp
        ?.map((item: any) => {
          if (!item?.steps) return item;

          return item?.steps?.map((step: any) => ({
            ...step,
            image: item?.image,
          }));
        })
        ?.flat()
        ?.map((item) => item?.realData || item);
    }

    res.experiences = getExperiences(obj, false);
    res.volunteers = getExperiences(obj, true);

    return res;
  }, [extractedProfileData]);

  return normalizedInitialData;
}
