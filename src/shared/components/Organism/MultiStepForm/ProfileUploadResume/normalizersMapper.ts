import { experienceNormalizer } from '@shared/utils/experience.utils';
import { courseNormalizer } from 'shared/utils/course.utils';
import { educationNormalizer } from 'shared/utils/education.utils';
import { licenceNormalizer } from 'shared/utils/licence.utils';
import languageNormalizer from 'shared/utils/normalizers/languageNormalizer';
import skillNormalizer from 'shared/utils/normalizers/skillNormalizer';
import {
  awardNormalizer,
  patentNormalizer,
  publicationNormalizer,
} from 'shared/utils/userAccomplishment.utils';

export const normalizersMapper: Record<string, Function> = {
  courses: courseNormalizer,
  experiences: experienceNormalizer,
  awards: awardNormalizer,
  languages: languageNormalizer,
  certifications: licenceNormalizer,
  patents: patentNormalizer,
  publications: publicationNormalizer,
  educations: educationNormalizer,
  skills: skillNormalizer,
};
