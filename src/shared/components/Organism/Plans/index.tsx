import React, { use<PERSON>emo, type MouseEvent } from 'react';
import NineDotPanelSteps from '@shared/constants/NineDotPanelSteps';
import {
  closeMultiStepForm,
  openMultiStepForm,
} from '@shared/hooks/useMultiStepForm';
import {
  selectData,
  setNineDotPanelState,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import useToast from '@shared/uikit/Toast/useToast';
import { enrollIntoPlan } from '@shared/utils/api/page';
import QueryKeys from '@shared/utils/constants/queryKeys';
import { getPortal } from '@shared/utils/getAppEnv';
import { useGetPlansData } from '@shared/utils/hooks/useGetPlansData';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  isPremiumPlan,
  type Plan,
} from '@shared/utils/normalizers/plansNormalizer';
import useCancelSubscription from './partails/useCancelSubscription';
import PlansModal from './PlansModal';
import PlansModalExpanded from './PlansModalExpanded';
import type { NineDotsMainStepsType } from '@shared/components/Organism/NineDotPanel/types';
import type { PlansModalData } from '@shared/components/Organism/Plans/types';

export default function Plans({
  onClose,
  handleNext,
}: {
  onClose?: (e: any) => void;
  handleNext?: (step?: NineDotsMainStepsType) => void;
}) {
  const toast = useToast();
  const { t } = useTranslation();
  const appPortal = getPortal().toUpperCase();

  const onOpenBilling = (e?: MouseEvent<any>) => {
    handleNext?.(NineDotPanelSteps.BILLINGS);
  };

  const {
    isExpanded,
    cart,
    timeSpan = 'MONTHLY',
  } = useNineDotPanelState<PlansModalData>(selectData);

  const { data, isLoading, refetch } = useGetPlansData({ appPortal, timeSpan });

  const enrollParamsRef = React.useRef<
    | {
        timeSpan: string;
        portalName: string;
        planName: string;
        numberOfSeats: number;
      }
    | undefined
  >(undefined);

  const { refetch: refetchPlanInvoice, isLoading: isBusy } = useReactQuery<any>(
    {
      action: {
        key: [QueryKeys.enrollIntoPlan, appPortal, timeSpan],
        apiFunc: () => enrollIntoPlan(enrollParamsRef.current as any),
        params: undefined,
      },
      config: {
        enabled: false,
      },
    }
  );

  const hasPremiumPlan = useMemo(
    () => data?.find(({ price }) => isPremiumPlan({ price })) !== undefined,
    [data]
  );
  const onSeeMore = () => () => {
    setNineDotPanelState({ data: { isExpanded: true, timeSpan } });
  };
  const onBack = () => {
    setNineDotPanelState({ data: { isExpanded: false, timeSpan } });
  };
  const onSelect = (plan?: Plan) => async (event?: MouseEvent<any>) => {
    if (!plan?.label) return;
    enrollParamsRef.current = {
      timeSpan,
      portalName: appPortal,
      planName: plan.label,
      numberOfSeats: 1,
    };
    const { data: invoice } = await refetchPlanInvoice();
    if (!invoice) return;
    setNineDotPanelState({ isOpen: false });

    openMultiStepForm({
      formName: 'checkout',
      data: {
        entityData: {
          entityId: plan?.id,
          label: plan?.title,
          subTitle: t(timeSpan.toLowerCase()),
          priceUnit: plan?.priceUnit,
          requestType: 'NEW_PLAN',
          Logo: plan?.Logo,
          enrollParams: enrollParamsRef.current,
        },
        invoice: {
          ...invoice,
          requestType: 'NEW_PLAN',
        },
        actions: {
          onCancel: () => {
            closeMultiStepForm('checkout');
            onBack();
            // setNineDotPanelState({
            //   isOpen: true,
            //   defaultActiveStep: NineDotPanelSteps.PLANS,
            //   data: {
            //     isExpanded,
            //     cart,
            //     timeSpan,
            //   },
            // });
          },
          onSuccess: () => {
            refetch();
          },
        },
      },
    });
  };

  const { onCancelSubscription } = useCancelSubscription({
    onSuccess: () => {
      toast({ message: 'success' });
      refetch();
    },
    confirmProps: {
      isNarrow: true,
      variant: 'normal',
    },
  });

  if (isLoading) return null;
  if (isExpanded)
    return (
      <PlansModalExpanded
        onBack={onBack}
        plans={data}
        onSelect={onSelect}
        hasPremiumPlan={hasPremiumPlan}
        onCancelSubscription={onCancelSubscription}
        handleOpenBillings={onOpenBilling}
        isBusy={isBusy}
      />
    );

  return (
    <PlansModal
      onClose={onClose}
      plans={data}
      onSeeMore={onSeeMore}
      onSelect={onSelect}
      hasPremiumPlan={hasPremiumPlan}
      onCancelSubscription={onCancelSubscription}
      handleOpenBillings={onOpenBilling}
    />
  );
}
