'use client';

import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';
import useDynamicFilters from '@shared/hooks/useDynamicFilters';
import { suggestObjects } from '@shared/utils/api/search';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import { responseProjectsNormalizer } from '@shared/utils/normalizers/projects';
import { searchFilterQueryParams } from 'shared/constants/search';
import { Endpoints, pageEndpoints } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './useSearchFiltersFields.module.scss';
import type { PaginateResponse } from 'shared/types/response';

const FORM_GROUP_CLASS = '!py-0 !mb-12';
const PLUS_BUTTON_CLASS = '!mt-8 !ml-8';

export const useRecruiterProjectsFilterGroups = () => {
  const dynamicFilters = useDynamicFilters();
  const { t } = useTranslation();
  const getQueryValue = useGetNormalizedArrayQuery();
  const { SORT_BY } = useCommonFilterGroups();

  const COLLABORATORS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('assignees'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.collaboratorUserIds,
    options: dynamicFilters.collaborators,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('assignees'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.collaboratorUserIds, 'array'),

    divider: { className: classes.groupDivider },
  };

  const CREATED_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('created_by'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.creatorIds,
    options: dynamicFilters.creators,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('created_by'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.creatorIds, 'array'),

    divider: { className: classes.groupDivider },
  };
  const JOBS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('linked_jobs'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.jobIds,
    options: dynamicFilters.jobs,
    label: t('linked_jobs'),
    getValue: () => getQueryValue(searchFilterQueryParams.jobIds, 'array'),
    placeholder: t('search_placeholder'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    isCustomEntryAllowed: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const TAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('tags'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.tags,
    options: dynamicFilters.tags,
    label: t('tags'),
    getValue: () => getQueryValue(searchFilterQueryParams.tags, 'array'),

    divider: { className: classes.groupDivider },
  };
  const CREATED_ON = {
    formGroup: {
      color: 'smoke_coal',
      title: t('created_on'),
      className: classes.header,
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.dateRangeType,
    divider: { className: classes.groupDivider },
    options: dynamicFilters.createOn,
    label: t('created_on'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.dateRangeType) || 'ANY_TIME',
    isDefaultValue:
      !getQueryValue(searchFilterQueryParams.dateRangeType) ||
      getQueryValue(searchFilterQueryParams.dateRangeType) === 'ANY_TIME',
  };

  const STATUS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('status'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'radioGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.status,
    options: dynamicFilters.status,
    label: t('status'),
    getValue: () => getQueryValue(searchFilterQueryParams.status),
    divider: { className: classes.groupDivider },
  };
  const OWNER = {
    formGroup: {
      color: 'smoke_coal',
      title: t('project_owner'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.ownerIds,
    divider: { className: classes.groupDivider },
    options: dynamicFilters.owners,
    label: t('project_owner'),
    placeholder: t('search_placeholder'),
    getValue: () => getQueryValue(searchFilterQueryParams.ownerIds, 'array'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      apiFunc: suggestObjects,
      normalizer: (response: any) =>
        response?.content?.map(
          ({ id, fullName, type, usernameAtSign }: any) => ({
            value: id,
            label: fullName,
            type,
            helperText: usernameAtSign,
          })
        ),
    },
  };

  const groups = [
    SORT_BY,
    CREATED_ON,
    STATUS,
    OWNER,
    COLLABORATORS,
    CREATED_BY,
    JOBS,
    TAGS,
  ];

  return groups.filter(Boolean);
};

type LookupResponseNormalizerType = Array<{ value: string; label: string }>;

export const responseCollaboratorsNormalizer = (
  response: PaginateResponse<{
    userId: string;
    profileInfo: { fullName: string };
  }>
): LookupResponseNormalizerType =>
  response?.content
    ?.map(({ profileInfo, userId }) => ({
      value: userId,
      label: profileInfo.fullName,
    }))
    .slice(0, 6);
