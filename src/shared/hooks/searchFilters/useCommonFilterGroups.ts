import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import { searchFilterQueryParams } from 'shared/constants/search';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useDynamicFilters from '../useDynamicFilters';
import classes from './useSearchFiltersFields.module.scss';

export const useCommonFilterGroups = () => {
  const { t } = useTranslation();
  const getQueryValue = useGetNormalizedArrayQuery();
  const dynamic = useDynamicFilters();
  const sortByOptions = dynamic?.sortBy || [];
  const datePosted = dynamic?.datePosted || [];

  const PAGE_NUMBER = {
    name: searchFilterQueryParams.page,
    cp: 'input',
    hiddenInHeader: true,
    hiddenInForm: true,
    getValue: () => getQueryValue(searchFilterQueryParams.page) || 0,
    divider: { className: classes.groupDivider },
  };

  const QUERY = {
    name: searchFilterQueryParams.query,
    cp: 'input',
    hiddenInHeader: true,
    hiddenInForm: true,
    getValue: () =>
      decodeURIComponent(getQueryValue(searchFilterQueryParams.query) || ''),
    divider: { className: classes.groupDivider },
  };

  const SORT_BY = sortByOptions?.length > 0 && {
    formGroup: {
      color: 'smoke_coal',
      title: t('sort_by'),
      className: classes.header,
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.sortBy,
    options: sortByOptions,
    label: t('sort_by'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.sortBy) || 'MOST_RELEVANT',
    hiddenInHeader:
      !getQueryValue(searchFilterQueryParams.sortBy) ||
      getQueryValue(searchFilterQueryParams.sortBy) === 'MOST_RELEVANT',
    alwaysShowInHeader:
      !!getQueryValue(searchFilterQueryParams.sortBy) &&
      getQueryValue(searchFilterQueryParams.sortBy) !== 'MOST_RELEVANT',
    isDefaultValue:
      !getQueryValue(searchFilterQueryParams.sortBy) ||
      getQueryValue(searchFilterQueryParams.sortBy) === 'MOST_RELEVANT',
    divider: { className: classes.groupDivider },
  };

  const DATE_POSTED = datePosted?.length && {
    formGroup: {
      color: 'smoke_coal',
      title: t('date_posted'),
      className: classes.header,
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.datePosted,
    divider: { className: classes.groupDivider },
    options: datePosted,
    label: t('date_posted'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.datePosted) || 'ANY_TIME',
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    isDefaultValue:
      !getQueryValue(searchFilterQueryParams.datePosted) ||
      getQueryValue(searchFilterQueryParams.datePosted) === 'ANY_TIME',
  };

  return {
    PAGE_NUMBER,
    QUERY,
    SORT_BY,
    DATE_POSTED,
  };
};
