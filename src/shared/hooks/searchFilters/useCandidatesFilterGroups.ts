'use client';

import { searchFilterQueryParams } from '@shared/constants/search';
import useDynamicFilters from '@shared/hooks/useDynamicFilters';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import geoApi from '@shared/utils/api/geo';
import { Endpoints, searchEndPoints } from '@shared/utils/constants';
import {
  CANDIDATE_STATUS_VALUES,
  CANDIDATES_SOURCE_MAP,
  GENDER_VALUES,
  NOTICE_PERIOD_VALUES,
  RELOCATION_STATUS_VALUES,
} from '@shared/utils/constants/enums';
import { sortBy } from '@shared/utils/constants/enums/jobsDb';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { hashtagNormalizer } from '@shared/utils/normalizers/hashtagNormalizer';
import hereApiResponseNormalizer from '@shared/utils/normalizers/hereApiResponseNormalizer';
import lookupResponseNormalizer, {
  lookupResponseNormalizerWithLabel,
} from '@shared/utils/normalizers/lookupResponseNormalizer';
import { responseProjectsNormalizer } from '@shared/utils/normalizers/projects';
import skillsResponseNormalizer from '@shared/utils/normalizers/skillsResponseNormalizer';
import classes from './useSearchFiltersFields.module.scss';
import type { CandidateSourceTypes } from '@shared/utils/constants/enums';
import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';

export function useCandidatesFilterGroups() {
  const { t } = useTranslation();
  const { handleChangeParams } = useCustomParams();
  const { authUser } = useGetAppObject();
  const countryCode = authUser?.location?.countryCode;
  const getQueryValue = useGetNormalizedArrayQuery();
  const dynamicFilters = useDynamicFilters();
  const sourceValue: CandidateSourceTypes =
    getQueryValue(searchFilterQueryParams.source) ?? 'ALL';
  const { SORT_BY } = useCommonFilterGroups();

  const SOURCE = {
    name: searchFilterQueryParams.source,
    cp: 'list',
    options: Object.values(CANDIDATES_SOURCE_MAP),
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    hiddenInForm: true,
    label:
      sourceValue !== 'ALL' && sourceValue in CANDIDATES_SOURCE_MAP
        ? t(CANDIDATES_SOURCE_MAP[sourceValue].label)
        : t('source'),
    value: sourceValue || 'ALL',
    getValue: () => sourceValue || 'ALL',
    onChange: (value: {
      query: string;
      pathname: 'ALL' | 'LOBOX' | 'MANUAL' | 'SOCIAL';
    }) => {
      if (value.pathname === 'ALL') {
        handleChangeParams({
          remove: [searchFilterQueryParams.source],
        });
      } else {
        handleChangeParams({
          add: { [searchFilterQueryParams.source]: value.pathname },
        });
      }
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const OPEN_TO_WORK = {
    formGroup: {
      color: 'smoke_coal',
      title: t('status'),
      className: classes.header,
    },
    cp: 'radioGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.openToWork,
    options: Object.values(CANDIDATE_STATUS_VALUES),
    label: t('status'),
    getValue: () => getQueryValue(searchFilterQueryParams.openToWork),
    schema: 'semi-transparent',
    divider: {
      className: classes.groupDivider,
    },
  };

  const TITLES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('titles'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.occupations,
    options: dynamicFilters.occupations,
    label: t('j_title'),
    getValue: () => getQueryValue(searchFilterQueryParams.occupations, 'array'),
    placeholder: t('search_j_title'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizerWithLabel,
    },
    isCustomEntryAllowed: true,
    divider: {
      className: classes.groupDivider,
    },
  };

  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: dynamicFilters.cities,
    label: t('locations'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    placeholder: t('search_location'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const SKILLS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('skills'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.skills,
    options: dynamicFilters.skills,
    label: t('skills'),
    getValue: () => getQueryValue(searchFilterQueryParams.skills, 'array'),
    placeholder: t('search_skill'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getSkills,
      normalizer: skillsResponseNormalizer,
      plusButtonClassName: classes.plusButtonClassName,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.languages,
    options: dynamicFilters.languages,
    label: t('languages'),
    getValue: () => getQueryValue(searchFilterQueryParams.languages, 'array'),
    placeholder: t('search_language'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const PROJECTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('projects'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.projectIds,
    options: dynamicFilters.projects,
    label: t('projects'),
    getValue: () => getQueryValue(searchFilterQueryParams.projectIds, 'array'),
    placeholder: t('search_placeholder'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    isCustomEntryAllowed: true,
    divider: {
      className: classes.groupDivider,
    },
  };

  const JOBS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('jobs'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.jobIds,
    options: dynamicFilters.jobs,
    label: t('jobs'),
    getValue: () => getQueryValue(searchFilterQueryParams.jobIds, 'array'),
    placeholder: t('search_placeholder'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    isCustomEntryAllowed: true,
    divider: {
      className: classes.groupDivider,
    },
  };

  const VENDOR = {
    formGroup: {
      color: 'smoke_coal',
      title: t('Vendor'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.vendorIds,
    options: dynamicFilters.vendors,
    label: t('Vendor'),
    getValue: () => getQueryValue(searchFilterQueryParams.vendorIds, 'array'),
    placeholder: t('search_placeholder'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    isCustomEntryAllowed: true,
    divider: {
      className: classes.groupDivider,
    },
  };

  const CLIENT = {
    formGroup: {
      color: 'smoke_coal',
      title: t('Client'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.clientIds,
    options: dynamicFilters.clients,
    label: t('Client'),
    getValue: () => getQueryValue(searchFilterQueryParams.clientIds, 'array'),
    placeholder: t('search_placeholder'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    isCustomEntryAllowed: true,
    divider: {
      className: classes.groupDivider,
    },
  };

  const TAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('tags'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.tags,
    options: dynamicFilters.tags,
    label: t('tags'),
    getValue: () => getQueryValue(searchFilterQueryParams.tags, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const TRAVEL_REQUIREMENTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('willing_to_travel'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.travelRequirements,
    options: dynamicFilters.travelRequirements,
    label: t('willing_to_travel'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.travelRequirements, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: dynamicFilters.hashtags,
    label: t('hashtags'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    placeholder: t('search_hashtag'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: searchEndPoints.suggestHashtag,
      normalizer: hashtagNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const MEMBER_SINCE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('member_since'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.memberSince,
    options: dynamicFilters.memberSince,
    label: t('member_since'),
    getValue: () => getQueryValue(searchFilterQueryParams.memberSince, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const REFERRAL_USERS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('referral_users'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.referralUserIds,
    options: dynamicFilters.referralUsers,
    label: t('referral_users'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.referralUserIds, 'array'),
    placeholder: t('search_referral_user'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const REFERRAL_COMPANIES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('referral_companies'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.referralCompanyIds,
    options: dynamicFilters.referralCompanies,
    label: t('referral_companies'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.referralCompanyIds, 'array'),
    placeholder: t('search_referral_company'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const CREATE_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('created_by'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.createByIds,
    options: dynamicFilters.createBy,
    label: t('created_by'),
    getValue: () => getQueryValue(searchFilterQueryParams.createByIds, 'array'),
    placeholder: t('search_creator'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const SCHOOLS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('schools'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.schoolIds,
    options: dynamicFilters.schools,
    label: t('schools'),
    getValue: () => getQueryValue(searchFilterQueryParams.schoolIds, 'array'),
    placeholder: t('search_school'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const DEGREES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('degrees'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.degreeIds,
    options: dynamicFilters.degrees,
    label: t('degrees'),
    getValue: () => getQueryValue(searchFilterQueryParams.degreeIds, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const MAJORS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('majors'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.majorIds,
    options: dynamicFilters.majors,
    label: t('majors'),
    getValue: () => getQueryValue(searchFilterQueryParams.majorIds, 'array'),
    placeholder: t('search_major'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.getMajors}`,
      normalizer: hereApiResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const PREFERRED_LOCATIONS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('preferred_locations'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.preferredLocationIds,
    options: dynamicFilters.preferredLocations,
    label: t('preferred_locations'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.preferredLocationIds, 'array'),
    placeholder: t('search_preferred_location'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const RELOCATION_STATUSES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('relocation_status'),
      className: classes.header,
    },
    cp: 'radioGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.relocationStatuses,
    options: Object.values(RELOCATION_STATUS_VALUES),
    label: t('relocation_status'),
    getValue: () => getQueryValue(searchFilterQueryParams.relocationStatuses),
    schema: 'semi-transparent',
    divider: {
      className: classes.groupDivider,
    },
  };

  const NOTICE_PERIODS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('notice_period'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.noticePeriods,
    options: Object.values(NOTICE_PERIOD_VALUES),
    label: t('notice_period'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.noticePeriods, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const GENDERS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('gender'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.genders,
    options: Object.values(GENDER_VALUES),
    label: t('gender'),
    getValue: () => getQueryValue(searchFilterQueryParams.genders, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };
  const AGE_RANGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('age_range'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.ageRanges,
    options: dynamicFilters.ageRanges,
    label: t('age_range'),
    getValue: () => getQueryValue(searchFilterQueryParams.ageRanges, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };
  const RACES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('race'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.races,
    options: dynamicFilters.races,
    label: t('race'),
    getValue: () => getQueryValue(searchFilterQueryParams.races, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };
  const VETERAN_STATUSES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('veteran_status'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.veteranStatuses,
    options: dynamicFilters.veteranStatuses,
    label: t('veteran_status'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.veteranStatuses, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };
  const DISABILITY_STATUSES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('disability_status'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.disabilityStatuses,
    options: dynamicFilters.disabilityStatuses,
    label: t('disability_status'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.disabilityStatuses, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const CURRENT_COMPANIES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('current_companies'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.currentCompanyIds,
    options: dynamicFilters.currentCompanies,
    label: t('current_companies'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.currentCompanyIds, 'array'),
    placeholder: t('search_current_company'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.suggestPlace,
      normalizer: hereApiResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const PREVIOUS_COMPANIES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('previous_companies'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.previousCompanyIds,
    options: dynamicFilters.previousCompanies,
    label: t('previous_companies'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.previousCompanyIds, 'array'),
    placeholder: t('search_previous_company'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.suggestPlace,
      normalizer: hereApiResponseNormalizer,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  const LAST_ACTIVITY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('last_activity'),
      className: classes.header,
    },
    cp: 'radioGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.lastActivity,
    options: dynamicFilters.lastActivity,
    label: t('last_activity'),
    getValue: () => getQueryValue(searchFilterQueryParams.lastActivity),
    schema: 'semi-transparent',

    divider: {
      className: classes.groupDivider,
    },
  };

  const EXPERIENCE_LEVELS_FILTER = {
    formGroup: {
      color: 'smoke_coal',
      title: t('exp_level'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.experienceLevels,
    options: dynamicFilters.experienceLevels,
    label: t('exp_level'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.experienceLevels, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const EMPLOYMENT_TYPES_FILTER = {
    formGroup: {
      color: 'smoke_coal',
      title: t('employment_type'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.employmentTypes,
    options: dynamicFilters.employmentTypes,
    label: t('employment_type'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.employmentTypes, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const WORK_PLACE_TYPES_FILTER = {
    formGroup: {
      color: 'smoke_coal',
      title: t('work_place_type'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.workPlaceTypes,
    options: dynamicFilters.workPlaceTypes,
    label: t('work_place_type'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.workPlaceTypes, 'array'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const SALARY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('salary_range'),
      className: '!py-0 !mb-12',
    },
    cp: 'salaryPicker',
    withConfirmation: false,
    name: searchFilterQueryParams.salaryRange,
    data: dynamicFilters?.salaryRanges ?? {},
    getValue: () => getQueryValue(searchFilterQueryParams.salaryRange),
    label: t('salary_range'),
    divider: {
      className: classes.groupDivider,
    },
  };

  const groups = [
    SOURCE,
    MEMBER_SINCE,
    TITLES,
    SKILLS,
    LANGUAGES,
    LOCATION,
    PROJECTS,
    CLIENT,
    VENDOR,
    JOBS,
    OPEN_TO_WORK,
    SORT_BY,
    SALARY,
    TAGS,
    HASHTAGS,
    TRAVEL_REQUIREMENTS,
    REFERRAL_USERS,
    REFERRAL_COMPANIES,
    CREATE_BY,
    SCHOOLS,
    DEGREES,
    MAJORS,
    PREFERRED_LOCATIONS,
    RELOCATION_STATUSES,
    NOTICE_PERIODS,
    GENDERS,
    AGE_RANGES,
    RACES,
    VETERAN_STATUSES,
    DISABILITY_STATUSES,
    CURRENT_COMPANIES,
    PREVIOUS_COMPANIES,
    LAST_ACTIVITY,
    EXPERIENCE_LEVELS_FILTER,
    EMPLOYMENT_TYPES_FILTER,
    WORK_PLACE_TYPES_FILTER,
  ];

  const hasValue = (group: any): boolean => {
    if (!group?.getValue) return false;

    const value = group.getValue();

    if (Array.isArray(value)) {
      return value.length > 0;
    }

    if (typeof value === 'string') {
      return value !== '' && value !== 'ALL';
    }

    if (typeof value === 'object' && value !== null) {
      return Object.keys(value).length > 0;
    }

    return value !== null && value !== undefined;
  };

  const sortedGroups = groups.filter(Boolean).sort((a: any, b: any) => {
    const aHasValue = hasValue(a);
    const bHasValue = hasValue(b);

    if (aHasValue === bHasValue) {
      return 0;
    }

    return bHasValue ? 1 : -1;
  });

  return sortedGroups.map((group, index) => {
    if (
      dynamicFilters?.limitedInPlan &&
      !group.hiddenInForm &&
      index >= dynamicFilters?.numberOfFilter
    ) {
      return {
        ...group,
        wrapStyle: '!blur-sm !pointer-events-none !overflow-hidden',
        hiddenInHeader: true,
        hiddenInForm: true,
      };
    }

    return group;
  });
}
